# Markdown Generator

Generate comprehensive Markdown documentation from file structures with rich CLI interface.

## Features

- **Interactive CLI**: Rich-based interface with progress bars and styled output
- **Clipboard Integration**: Automatically copy generated markdown to clipboard (default enabled)
- **Flexible File Filtering**: Support for extension groups, patterns, and .gitignore integration
- **Smart Content Handling**: Automatic encoding detection and content exclusion markers
- **Customizable Output**: Configurable depth, structure preview, and file ordering
- **Modern Dependencies**: Minimal, essential packages only

## Quick Start

### Using uv (Recommended)

```bash
# Run with interactive prompts
run.bat

# Or directly with uv
uv run python src/main.py --prompt
```

### Traditional Python

```bash
python src/main.py --prompt
```

## Interactive Mode

The tool features an optimized UX flow that prioritizes the most important decisions first:

### Quick Start (Recommended)
1. **"Use default settings?"** - Choose `y` for instant generation with clipboard copy
2. **Result**: Comprehensive markdown documentation copied to clipboard and saved to file

### Custom Configuration Flow
Choose `n` for custom settings, then follow this optimized sequence:

1. **Post-Processing** (most important decision first):
   - Copy to clipboard? (default: yes)
2. **File Types**: Extensions, predefined groups, or all files
3. **Optional Settings**: Depth, structure options, file ordering
4. **Exclusions**: .gitignore integration, custom patterns
5. **Input/Output**: Paths and filenames

## Usage Examples

```bash
# Interactive mode (prompts for all options)
uv run python src/main.py --prompt

# Specify input/output with interactive prompts for other options
uv run python src/main.py -i /path/to/source -op /path/to/output --prompt

# Non-interactive with specific options
uv run python src/main.py -i /path/to/source -op /path/to/output -of documentation.md -e py js html

# Disable clipboard copy
uv run python src/main.py -i /path/to/source --copy_to_clipboard=false
```

## Configuration Options

- **File Types**: Choose specific extensions or predefined groups (Python, Web, etc.)
- **Depth Control**: Limit directory traversal depth
- **Exclusions**: Use .gitignore patterns or custom exclusion rules
- **Structure Options**: Include/exclude empty directories, file ordering preferences
- **Output Control**: Custom filenames, cleanup options

## Dependencies

- **chardet**: Character encoding detection
- **gitignore-parser**: .gitignore file parsing
- **loguru**: Advanced logging
- **pyperclip**: Clipboard integration
- **PyYAML**: YAML configuration support
- **rich**: Terminal UI framework

## Project Structure

```
markdown_generator/
├── src/
│   └── main.py          # Main application
├── pyproject.toml       # Project configuration
├── run.bat             # Universal runner script
└── README.md           # This file
```
