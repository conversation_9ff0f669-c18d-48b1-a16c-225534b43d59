@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: Instaloader Upgrade Tool
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  Instaloader Upgrade Tool
ECHO ========================================
ECHO.

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: Show current instaloader version
ECHO [INFO] Current instaloader version:
uv run python -c "import instaloader; print(f'instaloader {instaloader.__version__}')" 2>nul || ECHO Not installed or not accessible
ECHO.

:: Upgrade instaloader
ECHO [INFO] Upgrading instaloader to latest version...
uv sync --upgrade-package instaloader

IF ERRORLEVEL 1 (
    ECHO [WARNING] uv sync failed, trying alternative method...
    uv pip install --upgrade instaloader
    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to upgrade instaloader
        PAUSE>NUL & EXIT /B
    ) ELSE (
        ECHO [SUCCESS] instaloader upgraded successfully
    )
) ELSE (
    ECHO [SUCCESS] instaloader upgraded successfully
)

:: Show new version
ECHO.
ECHO [INFO] New instaloader version:
uv run python -c "import instaloader; print(f'instaloader {instaloader.__version__}')"

ECHO.
ECHO Press any key to exit...
PAUSE >NUL
EXIT /B
