{"folders": [{"path": "./dir_tree_generator"}], "settings": {"python.defaultInterpreterPath": "./dir_tree_generator/.venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/__pycache__": true, "**/*.pyc": true, "**/.venv": true, "**/node_modules": true, "**/logs": true}}, "extensions": {"recommendations": ["ms-python.python", "ms-python.black-formatter", "ms-python.flake8", "charliermarsh.ruff"]}}