# Directory Tree Generator

A tool for generating visual representations of directory structures with rich CLI interface.

## Features

- Map directory structures with customizable filtering
- Display child count for each directory (number of immediate files and subdirectories)
- Exclude specific file extensions or patterns
- Include or exclude empty directories
- Optionally only include certain file extensions
- **Copy directory tree to clipboard** (default enabled for quick access)
- Save the visualization to a file
- Open the file with the default application (smart defaults based on clipboard usage)
- Remove the file after viewing (optional)
- Interactive CLI with rich prompts and smart UX flow
- Long path support for Windows

## Usage

```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -i "input/path" -op "output/path" -of "filename.md"
```

## Command Line Arguments

- `-i, --input` - Input directory or file path
- `-op, --output_path` - Output directory path
- `-of, --output_filename` - Output file name (default extension: .md)
- `-ie, --include_empty` - Include empty directories
- `-e, --exclude_extensions` - Extensions to exclude (space-separated)
- `-c, --copy_to_clipboard` - Copy directory tree to clipboard (default: True)
- `-o, --open_file` - Open the output file upon completion
- `-r, --remove_file` - Remove the output file after opening
- `-nc, --no_child_count` - Hide child count for directories
- `--prompt` - Interactive mode - prompt for all inputs

## Interactive Mode

The tool features an optimized UX flow that prioritizes the most important decisions first:

### Quick Start (Recommended)
1. **Input path**: Enter directory to scan
2. **"Use default settings?"** - Choose `y` for instant generation
3. **Result**: Directory tree copied to clipboard, file saved (no opening)

### Custom Configuration Flow
Choose `n` for custom settings, then follow this optimized sequence:

1. **Post-Processing** (most important decision first):
   - Copy to clipboard? (affects all other choices)
   - If yes: File opening defaults to `no` (you have content in clipboard)
   - If no: File opening defaults to `yes` (you need to see content)

2. **Input/Output**: Paths and filenames
3. **Display Options**: Empty directories, child counts
4. **File Filtering**: Include/exclude extensions

### Smart UX Benefits
- **Clipboard-first approach**: Most important decision comes first
- **Context-aware defaults**: Subsequent choices adapt to clipboard decision
- **Minimal steps**: Only asks relevant follow-up questions
- **Logical grouping**: Related options are grouped together

## Dependencies

Managed via `pyproject.toml` with uv package manager:
- `pyperclip` - Clipboard integration
- `rich` - Terminal UI and styling

## Project Structure

```
dir_tree_generator/
├── src/
│   └── main.py          # Main application entry point
├── pyproject.toml       # uv project configuration
├── uv.lock             # Dependency lock file (auto-generated)
├── run.bat             # Universal runner with uv integration
└── README.md           # This file
```
