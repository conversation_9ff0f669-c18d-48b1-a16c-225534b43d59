import argparse
from pathlib import Path
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich import box
from datetime import datetime
import os
import platform

from loguru import logger
from core.logger import logger_config
from core.config import URL_PREFIXES, CURRENT_PATH, DATE_FORMAT, TIME_FORMAT
from generate_url.url_from_clipboard import create_shortcut, get_clipboard_content, generate_shortcut_name

console = Console()

def parse_arguments():
    parser = argparse.ArgumentParser(description="Create a URL or folder shortcut from the clipboard content.")
    parser.add_argument('-op', '--output_path', type=str, help="Directory path to save the shortcut")
    parser.add_argument('--dateprefix', action='store_true', help="Prefix the filename with the current date")
    parser.add_argument('--timeprefix', action='store_true', help="Prefix the filename with the current time")
    parser.add_argument('--prompt', action='store_true', help="Prompt the user for input values")
    parser.add_argument('--levels', type=int, default=None, help="Number of levels to include in the filename for local paths")
    args = parser.parse_args()
    return args

def clear_console():
    if platform.system() == "Windows":
        os.system("cls")
    else:
        os.system("clear")

def ensure_directory_exists(directory: Path):
    directory.mkdir(parents=True, exist_ok=True)
    logger.info(f"Created output directory: {directory}")

def display_summary(args, shortcut_name, content):
    table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
    table.add_column("Parameter", style="dim", width=20)
    table.add_column("Value", style="bold cyan")
    table.add_row("Clipboard", content)
    table.add_row("Directory", str(args.output_path))
    table.add_row("Filename", shortcut_name)
    console.print(table)
    logger.info("Displayed configuration summary.")

def main():
    logger_config.setup_logger()
    while True:
        clear_console()
        args = parse_arguments()

        clipboard_content = get_clipboard_content()
        logger.info(f"Clipboard: {clipboard_content}")

        if clipboard_content and (clipboard_content.startswith(URL_PREFIXES) or Path(clipboard_content).is_dir()):

            formatted_date = datetime.now().strftime(DATE_FORMAT)
            formatted_time = datetime.now().strftime(TIME_FORMAT)

            date_prefix = f"{formatted_date}_" if args.dateprefix else ""
            time_prefix = f"{formatted_time}_" if args.timeprefix else ""
            is_local_path = Path(clipboard_content).is_dir()

            if is_local_path:
                path_parts = Path(clipboard_content).parts
                min_levels = 1
                max_levels = len(path_parts)
                default_levels = (min_levels + max_levels) // 2
                args.levels = args.levels if args.levels is not None else default_levels

            shortcut_name = generate_shortcut_name(clipboard_content, levels=args.levels, prefix=f"{date_prefix}{time_prefix}")

            console.print(f"Clipboard: [bold dim]{clipboard_content}[/bold dim]\n")

            if args.prompt:
                # Ask if user wants to use defaults
                use_defaults = Confirm.ask("Use default settings? (output: current dir, date prefix: yes, time prefix: no)", default=True)

                if use_defaults:
                    # Apply defaults
                    args.output_path = args.output_path or str(CURRENT_PATH)
                    args.dateprefix = True
                    args.timeprefix = False
                    # Keep the calculated levels for local paths
                else:
                    # Interactive prompts for custom settings
                    args.output_path = Prompt.ask("Output path", default=args.output_path or str(CURRENT_PATH))

                    dateprefix_input = Prompt.ask("Filename: Prefix with current date? (y/n)", default="yes")
                    args.dateprefix = dateprefix_input.lower() in ["yes", "y", "true", "1"]

                    if args.dateprefix:
                        timeprefix_input = Prompt.ask("Filename: Prefix with current time? (y/n)", default="no")
                        args.timeprefix = timeprefix_input.lower() in ["yes", "y", "true", "1"]
                    else:
                        args.timeprefix = False

                    if is_local_path:
                        while True:
                            levels_input = Prompt.ask(
                                f"Number of levels to include in the filename for local paths ({min_levels}-{max_levels})",
                                default=str(args.levels)
                            )
                            args.levels = int(levels_input)
                            if min_levels <= args.levels <= max_levels:
                                break
                            console.print(f"[bold red]Please enter a value between {min_levels} and {max_levels}.[/bold red]")

                    shortcut_name = generate_shortcut_name(clipboard_content, levels=args.levels, prefix=f"{date_prefix}{time_prefix}")
                    console.print(f"Preview of filename: [bold cyan]{shortcut_name}[/bold cyan]")

            date_prefix = f"{formatted_date}_" if args.dateprefix else ""
            time_prefix = f"{formatted_time}_" if args.timeprefix else ""
            shortcut_name = generate_shortcut_name(clipboard_content, levels=args.levels, prefix=f"{date_prefix}{time_prefix}")

            display_summary(args, shortcut_name, clipboard_content)

            if Confirm.ask("Do you want to proceed with the above configuration?", default="yes"):
                output_path = Path(args.output_path) if args.output_path else CURRENT_PATH
                ensure_directory_exists(output_path)

                create_shortcut(clipboard_content, output_path, prefix=f"{date_prefix}{time_prefix}", levels=args.levels)
                console.print(f"Shortcut created for: [bold cyan]{clipboard_content}[/bold cyan]")
                logger.info(f"Shortcut created for: {clipboard_content}")
                break
            else:
                console.print("Operation cancelled. Restarting...", style="bold red")
                logger.info("Operation cancelled by the user. Restarting...")
        else:
            console.print("The clipboard does not contain a valid URL or directory path.", style="bold red")
            logger.error("The clipboard does not contain a valid URL or directory path. Exiting.")
            break

if __name__ == "__main__":
    main()
