# URL Generator

Create URL or folder shortcuts from clipboard content with rich CLI interface.

## Features

- **Clipboard Integration**: Automatically detects URLs and folder paths from clipboard
- **Smart Filename Generation**: Creates meaningful shortcut names from URL domains or folder paths
- **Date/Time Prefixes**: Optional date and time prefixes for organized file naming
- **Interactive CLI**: Rich-based interface with styled prompts and confirmations
- **Flexible Output**: Configurable output directory and filename customization
- **Path Level Control**: Specify how many directory levels to include in filename

## Quick Start

Run `run.bat` to start the interactive URL generator (handles environment setup automatically)

## Usage

``bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -op "output/path" --dateprefix --timeprefix
``