{"folders": [{"path": "./dir_tree_generator", "folder_exclude_patterns": [".git", "__pycache__", "build", "dist", ".venv", "node_modules", ".backups", "logs"], "file_exclude_patterns": ["*.sublime-workspace", "*.pyc", "*.pyo", "*.swp", "*.tmp", "*.log", "uv.lock"]}], "settings": {"tab_size": 4, "default_line_ending": "unix", "translate_tabs_to_spaces": true, "ensure_newline_at_eof_on_save": true, "trim_trailing_white_space_on_save": true, "python_interpreter": "$project_path/dir_tree_generator/.venv/Scripts/python", "python_formatter": "black", "python_linter": "flake8", "python_format_on_save": false}, "build_systems": [{"name": "py__DirTreeGenerator - uv run", "cmd": ["uv", "run", "python", "src/main.py", "--prompt"], "working_dir": "$project_path/dir_tree_generator", "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)", "selector": "source.python", "shell": true}]}