# Dir ``

### File Structure

```
├── .cursorignore
├── .cursorrules
├── .gitignore
├── .vscodeignore
├── README.md
├── markdown_generator.md
├── py__MarkdownGenerator.sublime-project
├── py__MarkdownGenerator.sublime-workspace [-]
├── pyproject.toml
├── run.bat
└── src
    ├── main.old.py
    └── main.py
```

---

#### `.cursorignore`

    
    
    # =======================================================
    # -- OVERRIDES --
    # =======================================================
    
    # filenames: unsorted
    **/.what-is-this.md
    **/app.log.yml
    **/quit.blend
    **/Run History-1.5a.csv
    **/Search History-1.5a.csv
    **/Session-1.5a.backup.json
    **/Session-1.5a.json
    
    # dirs
    **/.backups/
    **/.specstory/
    **/__meta__/
    
    # types
    *.sublime-workspace
    *.sublime_session
    
    # paths: files
    **/*sync-conflict*.*
    **/user-data/**/ui_messages.json
    **/.specstory/history/.what-is-this.md

---

#### `.cursorrules`

    - Meticulously purge redundant code to avert future complicationsâ€”securely remove any consolidated, documented files in the process.
    - Always embody the natural, inherent clarity of your code without resorting to unnecessary verbosity.
    - Optimize developer ergonomics by ensuring intuitive navigation throughout the system.
    - Throughout any project, assess the projectstructure and Codebase to identify any shortcomings or potential improvements that ensure clarity, coherence, and impact.
    - Let the natural clarity of your code take center stage, avoiding any unnecessary verbosity-and write code that is effortlessly comprehensible by future developers.

---

#### `.gitignore`

    
    # =======================================================
    # DIRNAMES
    # =======================================================
    # dirs: python
    **/.cache/
    **/.env/
    **/.venv/
    **/__pycache__/
    **/env/
    **/venv/
    # dirs: react
    **/node_modules/
    # dirs: logs and temp
    **/build/
    **/cache/
    **/dist/
    **/logs/
    **/temp/
    **/tmp/
    
    # =======================================================
    # EXTENSIONS
    # =======================================================
    # extensions: media
    *.mp4
    *.mkv
    *.webm
    *.mp3
    *.wav
    # extensions: unsorted
    *.bin
    *.blend1
    *.dll
    *.DS_Store
    *.exe
    *.ini.bak
    *.ldb
    *.log
    *.pak
    *.pickle
    *.png
    *.prv.ppk
    *.prv.pub
    *.pyc
    *.pyo
    *.sublime-workspace
    *.sublime_session
    *.swp
    *.tmp
    
    # =======================================================
    # FILENAMES
    # =======================================================
    # filenames: unsorted
    **/.what-is-this.md
    **/app.log.yml
    **/quit.blend
    **/Run History-1.5a.csv
    **/Search History-1.5a.csv
    **/Session-1.5a.backup.json
    **/Session-1.5a.json

---

#### `.vscodeignore`

    # https://github.com/cline/cline/blob/main/.vscodeignore
    
    # Default
    .vscode/**
    .vscode-test/**
    out/**
    node_modules/**
    src/**
    .gitignore
    .yarnrc
    esbuild.js
    vsc-extension-quickstart.md
    **/tsconfig.json
    **/.eslintrc.json
    **/*.map
    **/*.ts
    **/.vscode-test.*
    
    # Ignore all webview-ui files except the build directory (https://github.com/microsoft/vscode-webview-ui-toolkit-samples/blob/main/frameworks/hello-world-react-cra/.vscodeignore)
    webview-ui/src/**
    webview-ui/public/**
    webview-ui/index.html
    webview-ui/README.md
    webview-ui/package.json
    webview-ui/package-lock.json
    webview-ui/node_modules/**
    **/.gitignore
    
    # Ignore docs
    docs/**
    
    # Fix issue where codicons don't get packaged (https://github.com/microsoft/vscode-extension-samples/issues/692)
    !node_modules/@vscode/codicons/dist/codicon.css
    !node_modules/@vscode/codicons/dist/codicon.ttf
    
    # Include default themes JSON files used in getTheme
    !src/integrations/theme/default-themes/**
    
    # Include icons
    !assets/icons/**
    
    
    # =======================================================
    # -- OVERRIDES --
    # =======================================================
    
    # filenames: unsorted
    **/.what-is-this.md
    **/app.log.yml
    **/quit.blend
    **/Run History-1.5a.csv
    **/Search History-1.5a.csv
    **/Session-1.5a.backup.json
    **/Session-1.5a.json
    
    # dirs
    **/.backups/
    **/.specstory/
    **/__meta__/
    
    # types
    *.sublime-workspace
    *.sublime_session
    
    # paths: files
    **/*sync-conflict*.*
    **/user-data/**/ui_messages.json
    **/.specstory/history/.what-is-this.md

---

#### `README.md`

```markdown
    # Markdown Generator
    
    Generate comprehensive Markdown documentation from file structures with rich CLI interface.
    
    ## Features
    
    - **Interactive CLI**: Rich-based interface with progress bars and styled output
    - **Flexible File Filtering**: Support for extension groups, patterns, and .gitignore integration
    - **Smart Content Handling**: Automatic encoding detection and content exclusion markers
    - **Customizable Output**: Configurable depth, structure preview, and file ordering
    - **Modern Dependencies**: Minimal, essential packages only
    
    ## Quick Start
    
    ### Using uv (Recommended)
    
    ```bash
    # Run with interactive prompts
    run.bat
    
    # Or directly with uv
    uv run python src/main.py --prompt
    ```
    
    ### Traditional Python
    
    ```bash
    python src/main.py --prompt
    ```
    
    ## Usage Examples
    
    ```bash
    # Interactive mode (prompts for all options)
    uv run python src/main.py --prompt
    
    # Specify input/output with interactive prompts for other options
    uv run python src/main.py -i /path/to/source -op /path/to/output --prompt
    
    # Non-interactive with specific options
    uv run python src/main.py -i /path/to/source -op /path/to/output -of documentation.md -e py js html
    ```
    
    ## Configuration Options
    
    - **File Types**: Choose specific extensions or predefined groups (Python, Web, etc.)
    - **Depth Control**: Limit directory traversal depth
    - **Exclusions**: Use .gitignore patterns or custom exclusion rules
    - **Structure Options**: Include/exclude empty directories, file ordering preferences
    - **Output Control**: Custom filenames, cleanup options
    
    ## Dependencies
    
    - **chardet**: Character encoding detection
    - **gitignore-parser**: .gitignore file parsing
    - **loguru**: Advanced logging
    - **PyYAML**: YAML configuration support
    - **rich**: Terminal UI framework
    
    ## Project Structure
    
    ```
    markdown_generator/
    ├── src/
    │   └── main.py          # Main application
    ├── pyproject.toml       # Project configuration
    ├── run.bat             # Universal runner script
    └── README.md           # This file
    ```
```

---

#### `markdown_generator.md`

```markdown
    # Dir `markdown_generator`
    
    ### File Structure
    
    ```
    ├── .cursorignore
    ├── .cursorrules
    ├── .gitignore
    ├── .vscodeignore
    ├── README.md
    ├── markdown_generator.md
    ├── py__MarkdownGenerator.sublime-project
    [-] ├── py__MarkdownGenerator.sublime-workspace
    ├── pyproject.toml
    ├── run.bat
    └── src
        └── main.py
    ```
    
    ---
    
    #### `.cursorignore`
    
        
        
        # =======================================================
        # -- OVERRIDES --
        # =======================================================
        
        # filenames: unsorted
        **/.what-is-this.md
        **/app.log.yml
        **/quit.blend
        **/Run History-1.5a.csv
        **/Search History-1.5a.csv
        **/Session-1.5a.backup.json
        **/Session-1.5a.json
        
        # dirs
        **/.backups/
        **/.specstory/
        **/__meta__/
        
        # types
        *.sublime-workspace
        *.sublime_session
        
        # paths: files
        **/*sync-conflict*.*
        **/user-data/**/ui_messages.json
        **/.specstory/history/.what-is-this.md
    
    ---
    
    #### `.cursorrules`
    
        - Meticulously purge redundant code to avert future complicationsâ€”securely remove any consolidated, documented files in the process.
        - Always embody the natural, inherent clarity of your code without resorting to unnecessary verbosity.
        - Optimize developer ergonomics by ensuring intuitive navigation throughout the system.
        - Throughout any project, assess the projectstructure and Codebase to identify any shortcomings or potential improvements that ensure clarity, coherence, and impact.
        - Let the natural clarity of your code take center stage, avoiding any unnecessary verbosity-and write code that is effortlessly comprehensible by future developers.
    
    ---
    
    #### `.gitignore`
    
        
        # =======================================================
        # DIRNAMES
        # =======================================================
        # dirs: python
        **/.cache/
        **/.env/
        **/.venv/
        **/__pycache__/
        **/env/
        **/venv/
        # dirs: react
        **/node_modules/
        # dirs: logs and temp
        **/build/
        **/cache/
        **/dist/
        **/logs/
        **/temp/
        **/tmp/
        
        # =======================================================
        # EXTENSIONS
        # =======================================================
        # extensions: media
        *.mp4
        *.mkv
        *.webm
        *.mp3
        *.wav
        # extensions: unsorted
        *.bin
        *.blend1
        *.dll
        *.DS_Store
        *.exe
        *.ini.bak
        *.ldb
        *.log
        *.pak
        *.pickle
        *.png
        *.prv.ppk
        *.prv.pub
        *.pyc
        *.pyo
        *.sublime-workspace
        *.sublime_session
        *.swp
        *.tmp
        
        # =======================================================
        # FILENAMES
        # =======================================================
        # filenames: unsorted
        **/.what-is-this.md
        **/app.log.yml
        **/quit.blend
        **/Run History-1.5a.csv
        **/Search History-1.5a.csv
        **/Session-1.5a.backup.json
        **/Session-1.5a.json
    
    ---
    
    #### `.vscodeignore`
    
        # https://github.com/cline/cline/blob/main/.vscodeignore
        
        # Default
        .vscode/**
        .vscode-test/**
        out/**
        node_modules/**
        src/**
        .gitignore
        .yarnrc
        esbuild.js
        vsc-extension-quickstart.md
        **/tsconfig.json
        **/.eslintrc.json
        **/*.map
        **/*.ts
        **/.vscode-test.*
        
        # Ignore all webview-ui files except the build directory (https://github.com/microsoft/vscode-webview-ui-toolkit-samples/blob/main/frameworks/hello-world-react-cra/.vscodeignore)
        webview-ui/src/**
        webview-ui/public/**
        webview-ui/index.html
        webview-ui/README.md
        webview-ui/package.json
        webview-ui/package-lock.json
        webview-ui/node_modules/**
        **/.gitignore
        
        # Ignore docs
        docs/**
        
        # Fix issue where codicons don't get packaged (https://github.com/microsoft/vscode-extension-samples/issues/692)
        !node_modules/@vscode/codicons/dist/codicon.css
        !node_modules/@vscode/codicons/dist/codicon.ttf
        
        # Include default themes JSON files used in getTheme
        !src/integrations/theme/default-themes/**
        
        # Include icons
        !assets/icons/**
        
        
        # =======================================================
        # -- OVERRIDES --
        # =======================================================
        
        # filenames: unsorted
        **/.what-is-this.md
        **/app.log.yml
        **/quit.blend
        **/Run History-1.5a.csv
        **/Search History-1.5a.csv
        **/Session-1.5a.backup.json
        **/Session-1.5a.json
        
        # dirs
        **/.backups/
        **/.specstory/
        **/__meta__/
        
        # types
        *.sublime-workspace
        *.sublime_session
        
        # paths: files
        **/*sync-conflict*.*
        **/user-data/**/ui_messages.json
        **/.specstory/history/.what-is-this.md
    
    ---
    
    #### `README.md`
    
    ```markdown
        # Markdown Generator
        
        Generate comprehensive Markdown documentation from file structures with rich CLI interface.
        
        ## Features
        
        - **Interactive CLI**: Rich-based interface with progress bars and styled output
        - **Flexible File Filtering**: Support for extension groups, patterns, and .gitignore integration
        - **Smart Content Handling**: Automatic encoding detection and content exclusion markers
        - **Customizable Output**: Configurable depth, structure preview, and file ordering
        - **Modern Dependencies**: Minimal, essential packages only
        
        ## Quick Start
        
        ### Using uv (Recommended)
        
        ```bash
        # Run with interactive prompts
        run.bat
        
        # Or directly with uv
        uv run python src/main.py --prompt
        ```
        
        ### Traditional Python
        
        ```bash
        python src/main.py --prompt
        ```
        
        ## Usage Examples
        
        ```bash
        # Interactive mode (prompts for all options)
        uv run python src/main.py --prompt
        
        # Specify input/output with interactive prompts for other options
        uv run python src/main.py -i /path/to/source -op /path/to/output --prompt
        
        # Non-interactive with specific options
        uv run python src/main.py -i /path/to/source -op /path/to/output -of documentation.md -e py js html
        ```
        
        ## Configuration Options
        
        - **File Types**: Choose specific extensions or predefined groups (Python, Web, etc.)
        - **Depth Control**: Limit directory traversal depth
        - **Exclusions**: Use .gitignore patterns or custom exclusion rules
        - **Structure Options**: Include/exclude empty directories, file ordering preferences
        - **Output Control**: Custom filenames, cleanup options
        
        ## Dependencies
        
        - **chardet**: Character encoding detection
        - **gitignore-parser**: .gitignore file parsing
        - **loguru**: Advanced logging
        - **PyYAML**: YAML configuration support
        - **rich**: Terminal UI framework
        
        ## Project Structure
        
        ```
        markdown_generator/
        ├── src/
        │   └── main.py          # Main application
        ├── pyproject.toml       # Project configuration
        ├── run.bat             # Universal runner script
        └── README.md           # This file
        ```
    ```
    
    ---
    
    #### `markdown_generator.md`
    
    ```markdown
        # Dir `markdown_generator`
        
        *Files marked `[-]` are excluded from content, `[ ]` files are included.*
        
        ### File Structure
        
        ```
        ├── [ ] .cursorignore
        ├── [ ] .cursorrules
        ├── [ ] .gitignore
        ├── [ ] .vscodeignore
        ├── [ ] README.md
        ├── [-] app.log.yml
        ├── [ ] py__MarkdownGenerator.sublime-project
        ├── [ ] py__MarkdownGenerator.sublime-workspace
        ├── [ ] pyproject.toml
        ├── [ ] run.bat
        ├── [-] uv.lock
        ├── .venv
        │   ├── [-] .gitignore
        │   ├── [-] .lock
        │   ├── [-] CACHEDIR.TAG
        │   ├── [-] pyvenv.cfg
        │   ├── Lib
        │   │   └── site-packages
        │   │       ├── [-] _virtualenv.pth
        │   │       ├── [-] _virtualenv.py
        │   │       ├── [-] gitignore_parser.py
        │   │       ├── PyYAML-6.0.2.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] LICENSE
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   └── [-] top_level.txt
        │   │       ├── __pycache__
        │   │       │   ├── [-] _virtualenv.cpython-313.pyc
        │   │       │   └── [-] gitignore_parser.cpython-313.pyc
        │   │       ├── _yaml
        │   │       │   └── [-] __init__.py
        │   │       ├── chardet
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] __main__.py
        │   │       │   ├── [-] big5freq.py
        │   │       │   ├── [-] big5prober.py
        │   │       │   ├── [-] chardistribution.py
        │   │       │   ├── [-] charsetgroupprober.py
        │   │       │   ├── [-] charsetprober.py
        │   │       │   ├── [-] codingstatemachine.py
        │   │       │   ├── [-] codingstatemachinedict.py
        │   │       │   ├── [-] cp949prober.py
        │   │       │   ├── [-] enums.py
        │   │       │   ├── [-] escprober.py
        │   │       │   ├── [-] escsm.py
        │   │       │   ├── [-] eucjpprober.py
        │   │       │   ├── [-] euckrfreq.py
        │   │       │   ├── [-] euckrprober.py
        │   │       │   ├── [-] euctwfreq.py
        │   │       │   ├── [-] euctwprober.py
        │   │       │   ├── [-] gb2312freq.py
        │   │       │   ├── [-] gb2312prober.py
        │   │       │   ├── [-] hebrewprober.py
        │   │       │   ├── [-] jisfreq.py
        │   │       │   ├── [-] johabfreq.py
        │   │       │   ├── [-] johabprober.py
        │   │       │   ├── [-] jpcntx.py
        │   │       │   ├── [-] langbulgarianmodel.py
        │   │       │   ├── [-] langgreekmodel.py
        │   │       │   ├── [-] langhebrewmodel.py
        │   │       │   ├── [-] langhungarianmodel.py
        │   │       │   ├── [-] langrussianmodel.py
        │   │       │   ├── [-] langthaimodel.py
        │   │       │   ├── [-] langturkishmodel.py
        │   │       │   ├── [-] latin1prober.py
        │   │       │   ├── [-] macromanprober.py
        │   │       │   ├── [-] mbcharsetprober.py
        │   │       │   ├── [-] mbcsgroupprober.py
        │   │       │   ├── [-] mbcssm.py
        │   │       │   ├── [-] py.typed
        │   │       │   ├── [-] resultdict.py
        │   │       │   ├── [-] sbcharsetprober.py
        │   │       │   ├── [-] sbcsgroupprober.py
        │   │       │   ├── [-] sjisprober.py
        │   │       │   ├── [-] universaldetector.py
        │   │       │   ├── [-] utf1632prober.py
        │   │       │   ├── [-] utf8prober.py
        │   │       │   ├── [-] version.py
        │   │       │   ├── __pycache__
        │   │       │   │   ├── [-] __init__.cpython-313.pyc
        │   │       │   │   ├── [-] big5freq.cpython-313.pyc
        │   │       │   │   ├── [-] big5prober.cpython-313.pyc
        │   │       │   │   ├── [-] chardistribution.cpython-313.pyc
        │   │       │   │   ├── [-] charsetgroupprober.cpython-313.pyc
        │   │       │   │   ├── [-] charsetprober.cpython-313.pyc
        │   │       │   │   ├── [-] codingstatemachine.cpython-313.pyc
        │   │       │   │   ├── [-] codingstatemachinedict.cpython-313.pyc
        │   │       │   │   ├── [-] cp949prober.cpython-313.pyc
        │   │       │   │   ├── [-] enums.cpython-313.pyc
        │   │       │   │   ├── [-] escprober.cpython-313.pyc
        │   │       │   │   ├── [-] escsm.cpython-313.pyc
        │   │       │   │   ├── [-] eucjpprober.cpython-313.pyc
        │   │       │   │   ├── [-] euckrfreq.cpython-313.pyc
        │   │       │   │   ├── [-] euckrprober.cpython-313.pyc
        │   │       │   │   ├── [-] euctwfreq.cpython-313.pyc
        │   │       │   │   ├── [-] euctwprober.cpython-313.pyc
        │   │       │   │   ├── [-] gb2312freq.cpython-313.pyc
        │   │       │   │   ├── [-] gb2312prober.cpython-313.pyc
        │   │       │   │   ├── [-] hebrewprober.cpython-313.pyc
        │   │       │   │   ├── [-] jisfreq.cpython-313.pyc
        │   │       │   │   ├── [-] johabfreq.cpython-313.pyc
        │   │       │   │   ├── [-] johabprober.cpython-313.pyc
        │   │       │   │   ├── [-] jpcntx.cpython-313.pyc
        │   │       │   │   ├── [-] langbulgarianmodel.cpython-313.pyc
        │   │       │   │   ├── [-] langgreekmodel.cpython-313.pyc
        │   │       │   │   ├── [-] langhebrewmodel.cpython-313.pyc
        │   │       │   │   ├── [-] langrussianmodel.cpython-313.pyc
        │   │       │   │   ├── [-] langthaimodel.cpython-313.pyc
        │   │       │   │   ├── [-] langturkishmodel.cpython-313.pyc
        │   │       │   │   ├── [-] latin1prober.cpython-313.pyc
        │   │       │   │   ├── [-] macromanprober.cpython-313.pyc
        │   │       │   │   ├── [-] mbcharsetprober.cpython-313.pyc
        │   │       │   │   ├── [-] mbcsgroupprober.cpython-313.pyc
        │   │       │   │   ├── [-] mbcssm.cpython-313.pyc
        │   │       │   │   ├── [-] resultdict.cpython-313.pyc
        │   │       │   │   ├── [-] sbcharsetprober.cpython-313.pyc
        │   │       │   │   ├── [-] sbcsgroupprober.cpython-313.pyc
        │   │       │   │   ├── [-] sjisprober.cpython-313.pyc
        │   │       │   │   ├── [-] universaldetector.cpython-313.pyc
        │   │       │   │   ├── [-] utf1632prober.cpython-313.pyc
        │   │       │   │   ├── [-] utf8prober.cpython-313.pyc
        │   │       │   │   └── [-] version.cpython-313.pyc
        │   │       │   ├── cli
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   └── [-] chardetect.py
        │   │       │   └── metadata
        │   │       │       ├── [-] __init__.py
        │   │       │       └── [-] languages.py
        │   │       ├── chardet-5.2.0.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] LICENSE
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   ├── [-] entry_points.txt
        │   │       │   └── [-] top_level.txt
        │   │       ├── colorama
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] ansi.py
        │   │       │   ├── [-] ansitowin32.py
        │   │       │   ├── [-] initialise.py
        │   │       │   ├── [-] win32.py
        │   │       │   ├── [-] winterm.py
        │   │       │   ├── __pycache__
        │   │       │   │   ├── [-] __init__.cpython-313.pyc
        │   │       │   │   ├── [-] ansi.cpython-313.pyc
        │   │       │   │   ├── [-] ansitowin32.cpython-313.pyc
        │   │       │   │   ├── [-] initialise.cpython-313.pyc
        │   │       │   │   ├── [-] win32.cpython-313.pyc
        │   │       │   │   └── [-] winterm.cpython-313.pyc
        │   │       │   └── tests
        │   │       │       ├── [-] __init__.py
        │   │       │       ├── [-] ansi_test.py
        │   │       │       ├── [-] ansitowin32_test.py
        │   │       │       ├── [-] initialise_test.py
        │   │       │       ├── [-] isatty_test.py
        │   │       │       ├── [-] utils.py
        │   │       │       └── [-] winterm_test.py
        │   │       ├── colorama-0.4.6.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   └── licenses
        │   │       │       └── [-] LICENSE.txt
        │   │       ├── gitignore_parser-0.1.12.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   ├── [-] top_level.txt
        │   │       │   └── licenses
        │   │       │       └── [-] LICENSE
        │   │       ├── loguru
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] __init__.pyi
        │   │       │   ├── [-] _asyncio_loop.py
        │   │       │   ├── [-] _better_exceptions.py
        │   │       │   ├── [-] _colorama.py
        │   │       │   ├── [-] _colorizer.py
        │   │       │   ├── [-] _contextvars.py
        │   │       │   ├── [-] _ctime_functions.py
        │   │       │   ├── [-] _datetime.py
        │   │       │   ├── [-] _defaults.py
        │   │       │   ├── [-] _error_interceptor.py
        │   │       │   ├── [-] _file_sink.py
        │   │       │   ├── [-] _filters.py
        │   │       │   ├── [-] _get_frame.py
        │   │       │   ├── [-] _handler.py
        │   │       │   ├── [-] _locks_machinery.py
        │   │       │   ├── [-] _logger.py
        │   │       │   ├── [-] _recattrs.py
        │   │       │   ├── [-] _simple_sinks.py
        │   │       │   ├── [-] _string_parsers.py
        │   │       │   ├── [-] py.typed
        │   │       │   └── __pycache__
        │   │       │       ├── [-] __init__.cpython-313.pyc
        │   │       │       ├── [-] _asyncio_loop.cpython-313.pyc
        │   │       │       ├── [-] _better_exceptions.cpython-313.pyc
        │   │       │       ├── [-] _colorama.cpython-313.pyc
        │   │       │       ├── [-] _colorizer.cpython-313.pyc
        │   │       │       ├── [-] _contextvars.cpython-313.pyc
        │   │       │       ├── [-] _ctime_functions.cpython-313.pyc
        │   │       │       ├── [-] _datetime.cpython-313.pyc
        │   │       │       ├── [-] _defaults.cpython-313.pyc
        │   │       │       ├── [-] _error_interceptor.cpython-313.pyc
        │   │       │       ├── [-] _file_sink.cpython-313.pyc
        │   │       │       ├── [-] _filters.cpython-313.pyc
        │   │       │       ├── [-] _get_frame.cpython-313.pyc
        │   │       │       ├── [-] _handler.cpython-313.pyc
        │   │       │       ├── [-] _locks_machinery.cpython-313.pyc
        │   │       │       ├── [-] _logger.cpython-313.pyc
        │   │       │       ├── [-] _recattrs.cpython-313.pyc
        │   │       │       ├── [-] _simple_sinks.cpython-313.pyc
        │   │       │       └── [-] _string_parsers.cpython-313.pyc
        │   │       ├── loguru-0.7.3.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   └── [-] WHEEL
        │   │       ├── markdown_it
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] _compat.py
        │   │       │   ├── [-] _punycode.py
        │   │       │   ├── [-] main.py
        │   │       │   ├── [-] parser_block.py
        │   │       │   ├── [-] parser_core.py
        │   │       │   ├── [-] parser_inline.py
        │   │       │   ├── [-] port.yaml
        │   │       │   ├── [-] py.typed
        │   │       │   ├── [-] renderer.py
        │   │       │   ├── [-] ruler.py
        │   │       │   ├── [-] token.py
        │   │       │   ├── [-] tree.py
        │   │       │   ├── [-] utils.py
        │   │       │   ├── cli
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   └── [-] parse.py
        │   │       │   ├── common
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] entities.py
        │   │       │   │   ├── [-] html_blocks.py
        │   │       │   │   ├── [-] html_re.py
        │   │       │   │   ├── [-] normalize_url.py
        │   │       │   │   └── [-] utils.py
        │   │       │   ├── helpers
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] parse_link_destination.py
        │   │       │   │   ├── [-] parse_link_label.py
        │   │       │   │   └── [-] parse_link_title.py
        │   │       │   ├── presets
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] commonmark.py
        │   │       │   │   ├── [-] default.py
        │   │       │   │   └── [-] zero.py
        │   │       │   ├── rules_block
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] blockquote.py
        │   │       │   │   ├── [-] code.py
        │   │       │   │   ├── [-] fence.py
        │   │       │   │   ├── [-] heading.py
        │   │       │   │   ├── [-] hr.py
        │   │       │   │   ├── [-] html_block.py
        │   │       │   │   ├── [-] lheading.py
        │   │       │   │   ├── [-] list.py
        │   │       │   │   ├── [-] paragraph.py
        │   │       │   │   ├── [-] reference.py
        │   │       │   │   ├── [-] state_block.py
        │   │       │   │   └── [-] table.py
        │   │       │   ├── rules_core
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] block.py
        │   │       │   │   ├── [-] inline.py
        │   │       │   │   ├── [-] linkify.py
        │   │       │   │   ├── [-] normalize.py
        │   │       │   │   ├── [-] replacements.py
        │   │       │   │   ├── [-] smartquotes.py
        │   │       │   │   ├── [-] state_core.py
        │   │       │   │   └── [-] text_join.py
        │   │       │   └── rules_inline
        │   │       │       ├── [-] __init__.py
        │   │       │       ├── [-] autolink.py
        │   │       │       ├── [-] backticks.py
        │   │       │       ├── [-] balance_pairs.py
        │   │       │       ├── [-] emphasis.py
        │   │       │       ├── [-] entity.py
        │   │       │       ├── [-] escape.py
        │   │       │       ├── [-] fragments_join.py
        │   │       │       ├── [-] html_inline.py
        │   │       │       ├── [-] image.py
        │   │       │       ├── [-] link.py
        │   │       │       ├── [-] linkify.py
        │   │       │       ├── [-] newline.py
        │   │       │       ├── [-] state_inline.py
        │   │       │       ├── [-] strikethrough.py
        │   │       │       └── [-] text.py
        │   │       ├── markdown_it_py-4.0.0.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   ├── [-] entry_points.txt
        │   │       │   └── licenses
        │   │       │       ├── [-] LICENSE
        │   │       │       └── [-] LICENSE.markdown-it
        │   │       ├── mdurl
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] _decode.py
        │   │       │   ├── [-] _encode.py
        │   │       │   ├── [-] _format.py
        │   │       │   ├── [-] _parse.py
        │   │       │   ├── [-] _url.py
        │   │       │   └── [-] py.typed
        │   │       ├── mdurl-0.1.2.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] LICENSE
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   └── [-] WHEEL
        │   │       ├── pygments
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] __main__.py
        │   │       │   ├── [-] cmdline.py
        │   │       │   ├── [-] console.py
        │   │       │   ├── [-] filter.py
        │   │       │   ├── [-] formatter.py
        │   │       │   ├── [-] lexer.py
        │   │       │   ├── [-] modeline.py
        │   │       │   ├── [-] plugin.py
        │   │       │   ├── [-] regexopt.py
        │   │       │   ├── [-] scanner.py
        │   │       │   ├── [-] sphinxext.py
        │   │       │   ├── [-] style.py
        │   │       │   ├── [-] token.py
        │   │       │   ├── [-] unistring.py
        │   │       │   ├── [-] util.py
        │   │       │   ├── filters
        │   │       │   │   └── [-] __init__.py
        │   │       │   ├── formatters
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] _mapping.py
        │   │       │   │   ├── [-] bbcode.py
        │   │       │   │   ├── [-] groff.py
        │   │       │   │   ├── [-] html.py
        │   │       │   │   ├── [-] img.py
        │   │       │   │   ├── [-] irc.py
        │   │       │   │   ├── [-] latex.py
        │   │       │   │   ├── [-] other.py
        │   │       │   │   ├── [-] pangomarkup.py
        │   │       │   │   ├── [-] rtf.py
        │   │       │   │   ├── [-] svg.py
        │   │       │   │   ├── [-] terminal.py
        │   │       │   │   └── [-] terminal256.py
        │   │       │   ├── lexers
        │   │       │   │   ├── [-] __init__.py
        │   │       │   │   ├── [-] _ada_builtins.py
        │   │       │   │   ├── [-] _asy_builtins.py
        │   │       │   │   ├── [-] _cl_builtins.py
        │   │       │   │   ├── [-] _cocoa_builtins.py
        │   │       │   │   ├── [-] _csound_builtins.py
        │   │       │   │   ├── [-] _css_builtins.py
        │   │       │   │   ├── [-] _googlesql_builtins.py
        │   │       │   │   ├── [-] _julia_builtins.py
        │   │       │   │   ├── [-] _lasso_builtins.py
        │   │       │   │   ├── [-] _lilypond_builtins.py
        │   │       │   │   ├── [-] _lua_builtins.py
        │   │       │   │   ├── [-] _luau_builtins.py
        │   │       │   │   ├── [-] _mapping.py
        │   │       │   │   ├── [-] _mql_builtins.py
        │   │       │   │   ├── [-] _mysql_builtins.py
        │   │       │   │   ├── [-] _openedge_builtins.py
        │   │       │   │   ├── [-] _php_builtins.py
        │   │       │   │   ├── [-] _postgres_builtins.py
        │   │       │   │   ├── [-] _qlik_builtins.py
        │   │       │   │   ├── [-] _scheme_builtins.py
        │   │       │   │   ├── [-] _scilab_builtins.py
        │   │       │   │   ├── [-] _sourcemod_builtins.py
        │   │       │   │   ├── [-] _sql_builtins.py
        │   │       │   │   ├── [-] _stan_builtins.py
        │   │       │   │   ├── [-] _stata_builtins.py
        │   │       │   │   ├── [-] _tsql_builtins.py
        │   │       │   │   ├── [-] _usd_builtins.py
        │   │       │   │   ├── [-] _vbscript_builtins.py
        │   │       │   │   ├── [-] _vim_builtins.py
        │   │       │   │   ├── [-] actionscript.py
        │   │       │   │   ├── [-] ada.py
        │   │       │   │   ├── [-] agile.py
        │   │       │   │   ├── [-] algebra.py
        │   │       │   │   ├── [-] ambient.py
        │   │       │   │   ├── [-] amdgpu.py
        │   │       │   │   ├── [-] ampl.py
        │   │       │   │   ├── [-] apdlexer.py
        │   │       │   │   ├── [-] apl.py
        │   │       │   │   ├── [-] archetype.py
        │   │       │   │   ├── [-] arrow.py
        │   │       │   │   ├── [-] arturo.py
        │   │       │   │   ├── [-] asc.py
        │   │       │   │   ├── [-] asm.py
        │   │       │   │   ├── [-] asn1.py
        │   │       │   │   ├── [-] automation.py
        │   │       │   │   ├── [-] bare.py
        │   │       │   │   ├── [-] basic.py
        │   │       │   │   ├── [-] bdd.py
        │   │       │   │   ├── [-] berry.py
        │   │       │   │   ├── [-] bibtex.py
        │   │       │   │   ├── [-] blueprint.py
        │   │       │   │   ├── [-] boa.py
        │   │       │   │   ├── [-] bqn.py
        │   │       │   │   ├── [-] business.py
        │   │       │   │   ├── [-] c_cpp.py
        │   │       │   │   ├── [-] c_like.py
        │   │       │   │   ├── [-] capnproto.py
        │   │       │   │   ├── [-] carbon.py
        │   │       │   │   ├── [-] cddl.py
        │   │       │   │   ├── [-] chapel.py
        │   │       │   │   ├── [-] clean.py
        │   │       │   │   ├── [-] codeql.py
        │   │       │   │   ├── [-] comal.py
        │   │       │   │   ├── [-] compiled.py
        │   │       │   │   ├── [-] configs.py
        │   │       │   │   ├── [-] console.py
        │   │       │   │   ├── [-] cplint.py
        │   │       │   │   ├── [-] crystal.py
        │   │       │   │   ├── [-] csound.py
        │   │       │   │   ├── [-] css.py
        │   │       │   │   ├── [-] d.py
        │   │       │   │   ├── [-] dalvik.py
        │   │       │   │   ├── [-] data.py
        │   │       │   │   ├── [-] dax.py
        │   │       │   │   ├── [-] devicetree.py
        │   │       │   │   ├── [-] diff.py
        │   │       │   │   ├── [-] dns.py
        │   │       │   │   ├── [-] dotnet.py
        │   │       │   │   ├── [-] dsls.py
        │   │       │   │   ├── [-] dylan.py
        │   │       │   │   ├── [-] ecl.py
        │   │       │   │   ├── [-] eiffel.py
        │   │       │   │   ├── [-] elm.py
        │   │       │   │   ├── [-] elpi.py
        │   │       │   │   ├── [-] email.py
        │   │       │   │   ├── [-] erlang.py
        │   │       │   │   ├── [-] esoteric.py
        │   │       │   │   ├── [-] ezhil.py
        │   │       │   │   ├── [-] factor.py
        │   │       │   │   ├── [-] fantom.py
        │   │       │   │   ├── [-] felix.py
        │   │       │   │   ├── [-] fift.py
        │   │       │   │   ├── [-] floscript.py
        │   │       │   │   ├── [-] forth.py
        │   │       │   │   ├── [-] fortran.py
        │   │       │   │   ├── [-] foxpro.py
        │   │       │   │   ├── [-] freefem.py
        │   │       │   │   ├── [-] func.py
        │   │       │   │   ├── [-] functional.py
        │   │       │   │   ├── [-] futhark.py
        │   │       │   │   ├── [-] gcodelexer.py
        │   │       │   │   ├── [-] gdscript.py
        │   │       │   │   ├── [-] gleam.py
        │   │       │   │   ├── [-] go.py
        │   │       │   │   ├── [-] grammar_notation.py
        │   │       │   │   ├── [-] graph.py
        │   │       │   │   ├── [-] graphics.py
        │   │       │   │   ├── [-] graphql.py
        │   │       │   │   ├── [-] graphviz.py
        │   │       │   │   ├── [-] gsql.py
        │   │       │   │   ├── [-] hare.py
        │   │       │   │   ├── [-] haskell.py
        │   │       │   │   ├── [-] haxe.py
        │   │       │   │   ├── [-] hdl.py
        │   │       │   │   ├── [-] hexdump.py
        │   │       │   │   ├── [-] html.py
        │   │       │   │   ├── [-] idl.py
        │   │       │   │   ├── [-] igor.py
        │   │       │   │   ├── [-] inferno.py
        │   │       │   │   ├── [-] installers.py
        │   │       │   │   ├── [-] int_fiction.py
        │   │       │   │   ├── [-] iolang.py
        │   │       │   │   ├── [-] j.py
        │   │       │   │   ├── [-] javascript.py
        │   │       │   │   ├── [-] jmespath.py
        │   │       │   │   ├── [-] jslt.py
        │   │       │   │   ├── [-] json5.py
        │   │       │   │   ├── [-] jsonnet.py
        │   │       │   │   ├── [-] jsx.py
        │   │       │   │   ├── [-] julia.py
        │   │       │   │   ├── [-] jvm.py
        │   │       │   │   ├── [-] kuin.py
        │   │       │   │   ├── [-] kusto.py
        │   │       │   │   ├── [-] ldap.py
        │   │       │   │   ├── [-] lean.py
        │   │       │   │   ├── [-] lilypond.py
        │   │       │   │   ├── [-] lisp.py
        │   │       │   │   ├── [-] macaulay2.py
        │   │       │   │   ├── [-] make.py
        │   │       │   │   ├── [-] maple.py
        │   │       │   │   ├── [-] markup.py
        │   │       │   │   ├── [-] math.py
        │   │       │   │   ├── [-] matlab.py
        │   │       │   │   ├── [-] maxima.py
        │   │       │   │   ├── [-] meson.py
        │   │       │   │   ├── [-] mime.py
        │   │       │   │   ├── [-] minecraft.py
        │   │       │   │   ├── [-] mips.py
        │   │       │   │   ├── [-] ml.py
        │   │       │   │   ├── [-] modeling.py
        │   │       │   │   ├── [-] modula2.py
        │   │       │   │   ├── [-] mojo.py
        │   │       │   │   ├── [-] monte.py
        │   │       │   │   ├── [-] mosel.py
        │   │       │   │   ├── [-] ncl.py
        │   │       │   │   ├── [-] nimrod.py
        │   │       │   │   ├── [-] nit.py
        │   │       │   │   ├── [-] nix.py
        │   │       │   │   ├── [-] numbair.py
        │   │       │   │   ├── [-] oberon.py
        │   │       │   │   ├── [-] objective.py
        │   │       │   │   ├── [-] ooc.py
        │   │       │   │   ├── [-] openscad.py
        │   │       │   │   ├── [-] other.py
        │   │       │   │   ├── [-] parasail.py
        │   │       │   │   ├── [-] parsers.py
        │   │       │   │   ├── [-] pascal.py
        │   │       │   │   ├── [-] pawn.py
        │   │       │   │   ├── [-] pddl.py
        │   │       │   │   ├── [-] perl.py
        │   │       │   │   ├── [-] phix.py
        │   │       │   │   ├── [-] php.py
        │   │       │   │   ├── [-] pointless.py
        │   │       │   │   ├── [-] pony.py
        │   │       │   │   ├── [-] praat.py
        │   │       │   │   ├── [-] procfile.py
        │   │       │   │   ├── [-] prolog.py
        │   │       │   │   ├── [-] promql.py
        │   │       │   │   ├── [-] prql.py
        │   │       │   │   ├── [-] ptx.py
        │   │       │   │   ├── [-] python.py
        │   │       │   │   ├── [-] q.py
        │   │       │   │   ├── [-] qlik.py
        │   │       │   │   ├── [-] qvt.py
        │   │       │   │   ├── [-] r.py
        │   │       │   │   ├── [-] rdf.py
        │   │       │   │   ├── [-] rebol.py
        │   │       │   │   ├── [-] rego.py
        │   │       │   │   ├── [-] resource.py
        │   │       │   │   ├── [-] ride.py
        │   │       │   │   ├── [-] rita.py
        │   │       │   │   ├── [-] rnc.py
        │   │       │   │   ├── [-] roboconf.py
        │   │       │   │   ├── [-] robotframework.py
        │   │       │   │   ├── [-] ruby.py
        │   │       │   │   ├── [-] rust.py
        │   │       │   │   ├── [-] sas.py
        │   │       │   │   ├── [-] savi.py
        │   │       │   │   ├── [-] scdoc.py
        │   │       │   │   ├── [-] scripting.py
        │   │       │   │   ├── [-] sgf.py
        │   │       │   │   ├── [-] shell.py
        │   │       │   │   ├── [-] sieve.py
        │   │       │   │   ├── [-] slash.py
        │   │       │   │   ├── [-] smalltalk.py
        │   │       │   │   ├── [-] smithy.py
        │   │       │   │   ├── [-] smv.py
        │   │       │   │   ├── [-] snobol.py
        │   │       │   │   ├── [-] solidity.py
        │   │       │   │   ├── [-] soong.py
        │   │       │   │   ├── [-] sophia.py
        │   │       │   │   ├── [-] special.py
        │   │       │   │   ├── [-] spice.py
        │   │       │   │   ├── [-] sql.py
        │   │       │   │   ├── [-] srcinfo.py
        │   │       │   │   ├── [-] stata.py
        │   │       │   │   ├── [-] supercollider.py
        │   │       │   │   ├── [-] tablegen.py
        │   │       │   │   ├── [-] tact.py
        │   │       │   │   ├── [-] tal.py
        │   │       │   │   ├── [-] tcl.py
        │   │       │   │   ├── [-] teal.py
        │   │       │   │   ├── [-] templates.py
        │   │       │   │   ├── [-] teraterm.py
        │   │       │   │   ├── [-] testing.py
        │   │       │   │   ├── [-] text.py
        │   │       │   │   ├── [-] textedit.py
        │   │       │   │   ├── [-] textfmts.py
        │   │       │   │   ├── [-] theorem.py
        │   │       │   │   ├── [-] thingsdb.py
        │   │       │   │   ├── [-] tlb.py
        │   │       │   │   ├── [-] tls.py
        │   │       │   │   ├── [-] tnt.py
        │   │       │   │   ├── [-] trafficscript.py
        │   │       │   │   ├── [-] typoscript.py
        │   │       │   │   ├── [-] typst.py
        │   │       │   │   ├── [-] ul4.py
        │   │       │   │   ├── [-] unicon.py
        │   │       │   │   ├── [-] urbi.py
        │   │       │   │   ├── [-] usd.py
        │   │       │   │   ├── [-] varnish.py
        │   │       │   │   ├── [-] verification.py
        │   │       │   │   ├── [-] verifpal.py
        │   │       │   │   ├── [-] vip.py
        │   │       │   │   ├── [-] vyper.py
        │   │       │   │   ├── [-] web.py
        │   │       │   │   ├── [-] webassembly.py
        │   │       │   │   ├── [-] webidl.py
        │   │       │   │   ├── [-] webmisc.py
        │   │       │   │   ├── [-] wgsl.py
        │   │       │   │   ├── [-] whiley.py
        │   │       │   │   ├── [-] wowtoc.py
        │   │       │   │   ├── [-] wren.py
        │   │       │   │   ├── [-] x10.py
        │   │       │   │   ├── [-] xorg.py
        │   │       │   │   ├── [-] yang.py
        │   │       │   │   ├── [-] yara.py
        │   │       │   │   └── [-] zig.py
        │   │       │   └── styles
        │   │       │       ├── [-] __init__.py
        │   │       │       ├── [-] _mapping.py
        │   │       │       ├── [-] abap.py
        │   │       │       ├── [-] algol.py
        │   │       │       ├── [-] algol_nu.py
        │   │       │       ├── [-] arduino.py
        │   │       │       ├── [-] autumn.py
        │   │       │       ├── [-] borland.py
        │   │       │       ├── [-] bw.py
        │   │       │       ├── [-] coffee.py
        │   │       │       ├── [-] colorful.py
        │   │       │       ├── [-] default.py
        │   │       │       ├── [-] dracula.py
        │   │       │       ├── [-] emacs.py
        │   │       │       ├── [-] friendly.py
        │   │       │       ├── [-] friendly_grayscale.py
        │   │       │       ├── [-] fruity.py
        │   │       │       ├── [-] gh_dark.py
        │   │       │       ├── [-] gruvbox.py
        │   │       │       ├── [-] igor.py
        │   │       │       ├── [-] inkpot.py
        │   │       │       ├── [-] lightbulb.py
        │   │       │       ├── [-] lilypond.py
        │   │       │       ├── [-] lovelace.py
        │   │       │       ├── [-] manni.py
        │   │       │       ├── [-] material.py
        │   │       │       ├── [-] monokai.py
        │   │       │       ├── [-] murphy.py
        │   │       │       ├── [-] native.py
        │   │       │       ├── [-] nord.py
        │   │       │       ├── [-] onedark.py
        │   │       │       ├── [-] paraiso_dark.py
        │   │       │       ├── [-] paraiso_light.py
        │   │       │       ├── [-] pastie.py
        │   │       │       ├── [-] perldoc.py
        │   │       │       ├── [-] rainbow_dash.py
        │   │       │       ├── [-] rrt.py
        │   │       │       ├── [-] sas.py
        │   │       │       ├── [-] solarized.py
        │   │       │       ├── [-] staroffice.py
        │   │       │       ├── [-] stata_dark.py
        │   │       │       ├── [-] stata_light.py
        │   │       │       ├── [-] tango.py
        │   │       │       ├── [-] trac.py
        │   │       │       ├── [-] vim.py
        │   │       │       ├── [-] vs.py
        │   │       │       ├── [-] xcode.py
        │   │       │       └── [-] zenburn.py
        │   │       ├── pygments-2.19.2.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   ├── [-] entry_points.txt
        │   │       │   └── licenses
        │   │       │       ├── [-] AUTHORS
        │   │       │       └── [-] LICENSE
        │   │       ├── rich
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] __main__.py
        │   │       │   ├── [-] _cell_widths.py
        │   │       │   ├── [-] _emoji_codes.py
        │   │       │   ├── [-] _emoji_replace.py
        │   │       │   ├── [-] _export_format.py
        │   │       │   ├── [-] _extension.py
        │   │       │   ├── [-] _fileno.py
        │   │       │   ├── [-] _inspect.py
        │   │       │   ├── [-] _log_render.py
        │   │       │   ├── [-] _loop.py
        │   │       │   ├── [-] _null_file.py
        │   │       │   ├── [-] _palettes.py
        │   │       │   ├── [-] _pick.py
        │   │       │   ├── [-] _ratio.py
        │   │       │   ├── [-] _spinners.py
        │   │       │   ├── [-] _stack.py
        │   │       │   ├── [-] _timer.py
        │   │       │   ├── [-] _win32_console.py
        │   │       │   ├── [-] _windows.py
        │   │       │   ├── [-] _windows_renderer.py
        │   │       │   ├── [-] _wrap.py
        │   │       │   ├── [-] abc.py
        │   │       │   ├── [-] align.py
        │   │       │   ├── [-] ansi.py
        │   │       │   ├── [-] bar.py
        │   │       │   ├── [-] box.py
        │   │       │   ├── [-] cells.py
        │   │       │   ├── [-] color.py
        │   │       │   ├── [-] color_triplet.py
        │   │       │   ├── [-] columns.py
        │   │       │   ├── [-] console.py
        │   │       │   ├── [-] constrain.py
        │   │       │   ├── [-] containers.py
        │   │       │   ├── [-] control.py
        │   │       │   ├── [-] default_styles.py
        │   │       │   ├── [-] diagnose.py
        │   │       │   ├── [-] emoji.py
        │   │       │   ├── [-] errors.py
        │   │       │   ├── [-] file_proxy.py
        │   │       │   ├── [-] filesize.py
        │   │       │   ├── [-] highlighter.py
        │   │       │   ├── [-] json.py
        │   │       │   ├── [-] jupyter.py
        │   │       │   ├── [-] layout.py
        │   │       │   ├── [-] live.py
        │   │       │   ├── [-] live_render.py
        │   │       │   ├── [-] logging.py
        │   │       │   ├── [-] markdown.py
        │   │       │   ├── [-] markup.py
        │   │       │   ├── [-] measure.py
        │   │       │   ├── [-] padding.py
        │   │       │   ├── [-] pager.py
        │   │       │   ├── [-] palette.py
        │   │       │   ├── [-] panel.py
        │   │       │   ├── [-] pretty.py
        │   │       │   ├── [-] progress.py
        │   │       │   ├── [-] progress_bar.py
        │   │       │   ├── [-] prompt.py
        │   │       │   ├── [-] protocol.py
        │   │       │   ├── [-] py.typed
        │   │       │   ├── [-] region.py
        │   │       │   ├── [-] repr.py
        │   │       │   ├── [-] rule.py
        │   │       │   ├── [-] scope.py
        │   │       │   ├── [-] screen.py
        │   │       │   ├── [-] segment.py
        │   │       │   ├── [-] spinner.py
        │   │       │   ├── [-] status.py
        │   │       │   ├── [-] style.py
        │   │       │   ├── [-] styled.py
        │   │       │   ├── [-] syntax.py
        │   │       │   ├── [-] table.py
        │   │       │   ├── [-] terminal_theme.py
        │   │       │   ├── [-] text.py
        │   │       │   ├── [-] theme.py
        │   │       │   ├── [-] themes.py
        │   │       │   ├── [-] traceback.py
        │   │       │   ├── [-] tree.py
        │   │       │   └── __pycache__
        │   │       │       ├── [-] __init__.cpython-313.pyc
        │   │       │       ├── [-] _cell_widths.cpython-313.pyc
        │   │       │       ├── [-] _emoji_codes.cpython-313.pyc
        │   │       │       ├── [-] _emoji_replace.cpython-313.pyc
        │   │       │       ├── [-] _export_format.cpython-313.pyc
        │   │       │       ├── [-] _extension.cpython-313.pyc
        │   │       │       ├── [-] _fileno.cpython-313.pyc
        │   │       │       ├── [-] _log_render.cpython-313.pyc
        │   │       │       ├── [-] _loop.cpython-313.pyc
        │   │       │       ├── [-] _null_file.cpython-313.pyc
        │   │       │       ├── [-] _palettes.cpython-313.pyc
        │   │       │       ├── [-] _pick.cpython-313.pyc
        │   │       │       ├── [-] _ratio.cpython-313.pyc
        │   │       │       ├── [-] _spinners.cpython-313.pyc
        │   │       │       ├── [-] _win32_console.cpython-313.pyc
        │   │       │       ├── [-] _windows.cpython-313.pyc
        │   │       │       ├── [-] _wrap.cpython-313.pyc
        │   │       │       ├── [-] abc.cpython-313.pyc
        │   │       │       ├── [-] align.cpython-313.pyc
        │   │       │       ├── [-] ansi.cpython-313.pyc
        │   │       │       ├── [-] box.cpython-313.pyc
        │   │       │       ├── [-] cells.cpython-313.pyc
        │   │       │       ├── [-] color.cpython-313.pyc
        │   │       │       ├── [-] color_triplet.cpython-313.pyc
        │   │       │       ├── [-] console.cpython-313.pyc
        │   │       │       ├── [-] constrain.cpython-313.pyc
        │   │       │       ├── [-] containers.cpython-313.pyc
        │   │       │       ├── [-] control.cpython-313.pyc
        │   │       │       ├── [-] default_styles.cpython-313.pyc
        │   │       │       ├── [-] emoji.cpython-313.pyc
        │   │       │       ├── [-] errors.cpython-313.pyc
        │   │       │       ├── [-] file_proxy.cpython-313.pyc
        │   │       │       ├── [-] filesize.cpython-313.pyc
        │   │       │       ├── [-] highlighter.cpython-313.pyc
        │   │       │       ├── [-] jupyter.cpython-313.pyc
        │   │       │       ├── [-] live.cpython-313.pyc
        │   │       │       ├── [-] live_render.cpython-313.pyc
        │   │       │       ├── [-] markup.cpython-313.pyc
        │   │       │       ├── [-] measure.cpython-313.pyc
        │   │       │       ├── [-] padding.cpython-313.pyc
        │   │       │       ├── [-] pager.cpython-313.pyc
        │   │       │       ├── [-] palette.cpython-313.pyc
        │   │       │       ├── [-] panel.cpython-313.pyc
        │   │       │       ├── [-] pretty.cpython-313.pyc
        │   │       │       ├── [-] progress.cpython-313.pyc
        │   │       │       ├── [-] progress_bar.cpython-313.pyc
        │   │       │       ├── [-] prompt.cpython-313.pyc
        │   │       │       ├── [-] protocol.cpython-313.pyc
        │   │       │       ├── [-] region.cpython-313.pyc
        │   │       │       ├── [-] repr.cpython-313.pyc
        │   │       │       ├── [-] scope.cpython-313.pyc
        │   │       │       ├── [-] screen.cpython-313.pyc
        │   │       │       ├── [-] segment.cpython-313.pyc
        │   │       │       ├── [-] spinner.cpython-313.pyc
        │   │       │       ├── [-] style.cpython-313.pyc
        │   │       │       ├── [-] styled.cpython-313.pyc
        │   │       │       ├── [-] table.cpython-313.pyc
        │   │       │       ├── [-] terminal_theme.cpython-313.pyc
        │   │       │       ├── [-] text.cpython-313.pyc
        │   │       │       ├── [-] theme.cpython-313.pyc
        │   │       │       └── [-] themes.cpython-313.pyc
        │   │       ├── rich-14.1.0.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] LICENSE
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   └── [-] WHEEL
        │   │       ├── win32_setctime
        │   │       │   ├── [-] __init__.py
        │   │       │   ├── [-] _setctime.py
        │   │       │   ├── [-] py.typed
        │   │       │   └── __pycache__
        │   │       │       ├── [-] __init__.cpython-313.pyc
        │   │       │       └── [-] _setctime.cpython-313.pyc
        │   │       ├── win32_setctime-1.2.0.dist-info
        │   │       │   ├── [-] INSTALLER
        │   │       │   ├── [-] LICENSE
        │   │       │   ├── [-] METADATA
        │   │       │   ├── [-] RECORD
        │   │       │   ├── [-] REQUESTED
        │   │       │   ├── [-] WHEEL
        │   │       │   └── [-] top_level.txt
        │   │       └── yaml
        │   │           ├── [-] __init__.py
        │   │           ├── [-] _yaml.cp313-win_amd64.pyd
        │   │           ├── [-] composer.py
        │   │           ├── [-] constructor.py
        │   │           ├── [-] cyaml.py
        │   │           ├── [-] dumper.py
        │   │           ├── [-] emitter.py
        │   │           ├── [-] error.py
        │   │           ├── [-] events.py
        │   │           ├── [-] loader.py
        │   │           ├── [-] nodes.py
        │   │           ├── [-] parser.py
        │   │           ├── [-] reader.py
        │   │           ├── [-] representer.py
        │   │           ├── [-] resolver.py
        │   │           ├── [-] scanner.py
        │   │           ├── [-] serializer.py
        │   │           ├── [-] tokens.py
        │   │           └── __pycache__
        │   │               ├── [-] __init__.cpython-313.pyc
        │   │               ├── [-] composer.cpython-313.pyc
        │   │               ├── [-] constructor.cpython-313.pyc
        │   │               ├── [-] cyaml.cpython-313.pyc
        │   │               ├── [-] dumper.cpython-313.pyc
        │   │               ├── [-] emitter.cpython-313.pyc
        │   │               ├── [-] error.cpython-313.pyc
        │   │               ├── [-] events.cpython-313.pyc
        │   │               ├── [-] loader.cpython-313.pyc
        │   │               ├── [-] nodes.cpython-313.pyc
        │   │               ├── [-] parser.cpython-313.pyc
        │   │               ├── [-] reader.cpython-313.pyc
        │   │               ├── [-] representer.cpython-313.pyc
        │   │               ├── [-] resolver.cpython-313.pyc
        │   │               ├── [-] scanner.cpython-313.pyc
        │   │               ├── [-] serializer.cpython-313.pyc
        │   │               └── [-] tokens.cpython-313.pyc
        │   └── Scripts
        │       ├── [-] activate
        │       ├── [-] activate.bat
        │       ├── [-] activate.csh
        │       ├── [-] activate.fish
        │       ├── [-] activate.nu
        │       ├── [-] activate.ps1
        │       ├── [-] activate_this.py
        │       ├── [-] chardetect.exe
        │       ├── [-] deactivate.bat
        │       ├── [-] markdown-it.exe
        │       ├── [-] pydoc.bat
        │       ├── [-] pygmentize.exe
        │       ├── [-] python.exe
        │       └── [-] pythonw.exe
        └── src
            └── [ ] main.py
        ```
        
        ---
        
        #### `src\main.py`
        
        ```python
            import argparse
            import fnmatch
            import os
            import platform
            import re
            import sys
            from pathlib import Path
            
            import chardet
            import yaml
            from gitignore_parser import parse_gitignore
            from loguru import logger
            from rich import box
            from rich.console import Console
            from rich.prompt import Confirm, Prompt
            from rich.progress import (
                Progress,
                SpinnerColumn,
                TimeElapsedColumn,
                TextColumn,
                BarColumn,
                MofNCompleteColumn,
            )
            from rich.table import Table
            
            class Config:
            
                # Configuration Constants
                # =======================================================
                DEFAULT_DEPTH = 99
                DEFAULT_EXTENSIONS = ["py"]
                DEFAULT_INCLUDE_ALL_FILES = True
                DEFAULT_PREDEFINED_EXTENSION_GROUPS = False
                DEFAULT_FULL_STRUCTURE_PREVIEW = False
                DEFAULT_CLEANUP_LOGS = True
                DEFAULT_FILES_FIRST = True
            
                # Directory exclusions - always exclude completely
                EXCLUDED_DIRS = [
                    ".backups", ".github", ".git", ".gpt", ".ignore",
                    ".old", ".cmd", ".tmp", ".venv", ".versions", ".specstory",
                    "__meta__", "__pycache__", "__tmp__", "venv", "node_modules",
                ]
            
                # Complete exclusions (excluded from both structure and content)
                EXCLUDED_PATTERNS = [
                    # Compiled/bytecode files
                    "*.pyc", "*.pyo", "*.class", 
                    # Database files
                    "*.db", 
                    # Binary/executable files
                    "*.exe", "*.dll", "*.so", "*.dylib", "*.bin",
                    # Log files
                    "*.log", "*.log.yml",  "uv.lock",
                    # Special files
                    ".new_hashes.py", ".original_hashes.py",
                    # Commented out but kept for reference
                    # "*.bat"
                ]
            
                # Regex patterns for complete exclusion
                EXCLUDED_REGEX = [r".*\.tmp$"]
            
                # Content-only exclusions (show in structure but exclude from content)
                CONTENT_ONLY_EXCLUDED_PATTERNS = [
                    # Image files
                    "*.ico", "*.png", "*.jpg", "*.jpeg", "*.heic", "*.gif", "*.bmp", "*.svg",
                    # Audio files
                    "*.mp3", "*.wav", "*.ogg", "*.flac", "*.aac",
                    # Video files
                    "*.mp4", "*.avi", "*.mov", "*.mkv", "*.webm", "*.m4a",
                    # Document files
                    "*.pdf", "*.docx", "*.xlsx", "*.pptx",
                    # Font files
                    "*.ttf", "*.otf", "*.woff", "*.woff2",
                    # Archive files
                    "*.zip", "*.tar", "*.gz", "*.rar", "*.7z",
                    # IDE files
                    "*.sublime-workspace"
                ]
                
                # Regex patterns for content-only exclusion
                CONTENT_ONLY_EXCLUDED_REGEX = []
            
                # Default markers for visual indicators
                CONTENT_EXCLUDED_MARKER = "[-]"  # Marker for files in structure but excluded from content
                CONTENT_INCLUDED_MARKER = "[ ]"  # Marker for files in structure and included in content
            
                EXTENSION_GROUPS = {
                    # "SublimeText": ["py", "*sublime*", "tmLanguage", "tmPreferences", "tmTheme", "stTheme"],
                    "SublimeText": ["py", "sublime-commands", "sublime-keymap", "sublime-settings", "sublime-menu"],
                    "Python": ["*pdm*", "env", "py", "pyi", "pyo", "toml", "jinja*"],
                    "bat|py|txt": ["bat", "py", "txt"],
                    "Web:React": ["ts", "tsx", "js", "json", "cjs", "css", "html", ],
                    # "Web": ["css", "html", "js"],
                    # "Data": ["cfg", "csv", "json", "xml"],
                }
            
                CODE_BLOCK_TYPES = {
                    "py": "python", "json": "json", "nss": "java", "log": "text", "txt": "text",
                    "md": "markdown", "html": "html", "htm": "html", "css": "css", "js": "javascript",
                    "ts": "typescript", "xml": "xml", "yaml": "yaml", "yml": "yaml", "sh": "bash",
                    "bat": "batch", "ini": "ini", "cfg": "ini", "java": "java", "c": "c", "cpp": "cpp",
                    "h": "cpp", "hpp": "cpp", "cs": "csharp", "go": "go", "rb": "ruby", "php": "php",
                    "sql": "sql", "swift": "swift", "kt": "kotlin", "rs": "rust", "r": "r", "pl": "perl",
                    "lua": "lua", "scala": "scala", "vb": "vbnet",
                }
            
            class LoggerSetup:
            
                # Logging
                # =======================================================
                @staticmethod
                def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
                    def yaml_sink(message):
                        record = message.record
            
                        time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"
                        level_str = f"!{record['level'].name}"
                        name_str = record['name']
                        funcName_str = f"*{record['function']}"
                        lineno_int = record["line"]
                        msg = record["message"]
            
                        if "\n" in msg:
                            lines = msg.split("\n")
                            message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
                        else:
                            # Quote message if it has special characters
                            if ":" in msg:
                                message_str = f"'{msg}'"
                            else:
                                message_str = msg
            
                        yaml_lines = [
                            f"- time: {time_str}",
                            f"  level: {level_str}",
                            f"  name: {name_str}",
                            f"  funcName: {funcName_str}",
                            f"  lineno: {lineno_int}",
                            f"  message: {message_str}",
                            ""
                        ]
            
                        with open(log_file, "a", encoding="utf-8") as f:
                            f.write("\n".join(yaml_lines) + "\n")
            
                    logger.remove()
                    logger.add(yaml_sink, level=level, enqueue=True)
            
                @staticmethod
                def initialize_logging():
                    LoggerSetup.setup_yaml_logging()
            
            class ArgumentHandler:
            
                # Argument Parsing and Prompting
                # =======================================================
                def __init__(self):
                    self.parser = self.parse_arguments()
            
                @staticmethod
                def parse_arguments():
                    logger.debug("Setting up argument parser.")
                    parser = argparse.ArgumentParser(description="Generate Markdown documentation from a file structure.")
                    parser.add_argument('-i', '--input_path', type=str, help="Input directory path")
                    parser.add_argument('-op', '--output_path', type=str, help="Output directory path")
                    parser.add_argument('-of', '--output_filename', type=str, help="Output markdown filename")
                    parser.add_argument('-d', '--depth', type=int, help="Max directory depth", default=Config.DEFAULT_DEPTH)
                    parser.add_argument('--include_all_files', action='store_true', help="Include all file types", default=Config.DEFAULT_INCLUDE_ALL_FILES)
                    parser.add_argument('-e', '--extensions', nargs='+', help="File extensions/groups", default=Config.DEFAULT_EXTENSIONS)
                    parser.add_argument('--use-gitignore', action='store_true', help="Use .gitignore for exclusions", default=False)
                    parser.add_argument('-edp', '--exclude_dir_patterns', nargs='+', help="Excluded directory patterns")
                    parser.add_argument('-efp', '--exclude_file_patterns', nargs='+', help="Excluded file patterns")
                    parser.add_argument('--show_all_files_in_filestructure', action='store_true', default=Config.DEFAULT_FULL_STRUCTURE_PREVIEW, help="Show all files in structure")
                    parser.add_argument('--include_empty_dirs', action='store_true', help="Include empty directories")
                    parser.add_argument('--files_first', action='store_true', default=Config.DEFAULT_FILES_FIRST, help="List files before directories in the output")
                    parser.add_argument('--prompt', action='store_true', help="Prompt for input values")
                    parser.add_argument('--log_to_current_dir', action='store_true', help="Log to current dir")
            
                    # Arguments for Log Cleanup
                    cleanup_logs_group = parser.add_mutually_exclusive_group()
                    cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true', help="Clean up log files after successful execution")
                    cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false', help="Do not clean up log files after successful execution")
                    parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)
            
                    logger.debug("Argument parser setup complete.")
                    return parser
            
                def get_arguments(self):
                    return self.parser.parse_args()
            
                def get_user_inputs(self, args):
                    """Get user inputs following the pattern from main.old.py"""
                    logger.debug("Getting user inputs.")
                    console = Console()
            
                    def print_section(title):
                        console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)
            
                    # If --prompt is used, ask for all configuration options
                    if args.prompt:
                        # Default Settings prompt comes first, following main.old.py pattern
                        print_section("Default Settings")
                        use_defaults = Confirm.ask("Use default settings?", default=True)
                        logger.debug(f"Use defaults: {use_defaults}")
            
                        if not use_defaults:
                            # --- File Types ---
                            print_section("File Types")
                            args.include_all_files = Confirm.ask("Include all file types?", default=args.include_all_files)
            
                            if not args.include_all_files:
                                if Confirm.ask("Use predefined extension group?", default=Config.DEFAULT_PREDEFINED_EXTENSION_GROUPS):
                                    group_names = list(Config.EXTENSION_GROUPS.keys())
                                    table = Table(header_style="bold magenta", box=box.SIMPLE)
                                    table.add_column("No.", style="bold cyan", justify="right")
                                    table.add_column("Group Name", style="bold cyan")
                                    table.add_column("Extensions", style="magenta")
                                    for i, group in enumerate(group_names, 1):
                                        exts = ', '.join(Config.EXTENSION_GROUPS[group])
                                        table.add_row(str(i), group, exts)
                                    console.print(table)
                                    choice = Prompt.ask("Choose group by number:", choices=[str(i) for i in range(1, len(group_names) + 1)])
                                    selected_group = group_names[int(choice) - 1]
                                    args.extensions = Config.EXTENSION_GROUPS[selected_group]
                                else:
                                    exts_input = Prompt.ask("File extensions (space-separated):", default=' '.join(args.extensions or Config.DEFAULT_EXTENSIONS))
                                    args.extensions = exts_input.split()
            
                            # --- Optional Settings ---
                            print_section("Optional Settings")
                            depth_input = Prompt.ask("Max depth:", default=str(args.depth))
                            args.depth = int(depth_input) if depth_input.isdigit() else Config.DEFAULT_DEPTH
            
                            if not args.include_all_files:
                                args.show_all_files_in_filestructure = Confirm.ask("Include unmatched files?", default=args.show_all_files_in_filestructure)
            
                            args.include_empty_dirs = Confirm.ask("Include empty dirs?", default=args.include_empty_dirs)
                            args.files_first = Confirm.ask("List files before directories?", default=args.files_first)
            
                            # --- Exclusions ---
                            print_section("Exclusions")
                            args.use_gitignore = Confirm.ask("Use .gitignore?", default=args.use_gitignore)
                            excl_dir = Prompt.ask("Exclude folders:", default=' '.join(args.exclude_dir_patterns or []))
                            args.exclude_dir_patterns = excl_dir.split() if excl_dir else []
                            excl_file = Prompt.ask("Exclude files:", default=' '.join(args.exclude_file_patterns or []))
                            args.exclude_file_patterns = excl_file.split() if excl_file else []
            
                            # --- Input/Output ---
                            print_section("Input/Output")
                            if not args.input_path:
                                args.input_path = Prompt.ask("Input folder:", default=args.input_path)
                            if not args.output_path:
                                args.output_path = Prompt.ask("Output folder:", default=args.output_path)
                            if not args.output_filename:
                                args.output_filename = Prompt.ask("Output file (.md):", default=args.output_filename or 'py__MarkdownGenerator.md')
            
                            # --- Cleanup Logs ---
                            args.cleanup_logs = Confirm.ask("Clean up log files after successful execution?", default=args.cleanup_logs)
                        else:
                            # Assign defaults if using default settings
                            args.input_path = args.input_path or ""
                            args.output_path = args.output_path or ""
                            args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
                            args.depth = args.depth or Config.DEFAULT_DEPTH
                            args.include_all_files = args.include_all_files or False
                            args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
                            args.use_gitignore = args.use_gitignore or False
                            args.exclude_dir_patterns = args.exclude_dir_patterns or []
                            args.exclude_file_patterns = args.exclude_file_patterns or []
                            args.show_all_files_in_filestructure = args.show_all_files_in_filestructure or Config.DEFAULT_FULL_STRUCTURE_PREVIEW
                            args.include_empty_dirs = args.include_empty_dirs or False
                            args.files_first = args.files_first or Config.DEFAULT_FILES_FIRST
                            args.cleanup_logs = args.cleanup_logs
            
                    # Ensure defaults for any unset values
                    args.input_path = args.input_path or os.getcwd()
                    args.output_path = args.output_path or os.getcwd()
                    args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
                    args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
                    args.exclude_dir_patterns = args.exclude_dir_patterns or []
                    args.exclude_file_patterns = args.exclude_file_patterns or []
            
                    logger.debug("User input collection complete.")
                    return args
            
            class FileExcluder:
            
                # File Exclusion Logic
                # =======================================================
                def __init__(self, root_dir: Path, exclude_dir_patterns=None, exclude_file_patterns=None,
                             exclude_regex=None, use_gitignore=False):
                    self.root_dir = root_dir
                    self.exclude_dir_patterns = exclude_dir_patterns or []
                    self.exclude_file_patterns = exclude_file_patterns or []
                    self.exclude_regex = exclude_regex or []
                    self.gitignore_patterns = self.load_gitignore_patterns() if use_gitignore else []
                    self.exclusion_counters = {"dirs": 0, "patterns": 0, "regex": 0, "gitignore": 0, "content_only": 0}
                    # Track files that should be excluded from content but shown in structure
                    self.content_only_excluded_files = set()
            
                def load_gitignore_patterns(self):
                    gitignore_path = self.root_dir / '.gitignore'
                    if not gitignore_path.exists():
                        logger.debug(f"No .gitignore found at {gitignore_path}")
                        return []
            
                    with open(gitignore_path, 'r') as file:
                        lines = file.readlines()
            
                    patterns = []
                    for line in lines:
                        stripped = line.strip()
                        if stripped and not stripped.startswith('#'):
                            if stripped.endswith('/'):
                                stripped = stripped.rstrip('/') + '/**'
                            elif stripped.startswith('/'):
                                stripped = stripped.lstrip('/')
                            patterns.append(stripped)
                    logger.debug(f".gitignore patterns: {patterns}")
                    return patterns
            
                def is_content_excluded(self, path: Path) -> bool:
                    """
                    Check if a file should be excluded from content but shown in structure.
                    Returns True if the file should be excluded from content only.
                    """
                    if path.is_dir():
                        return False  # Directories are never content-excluded
            
                    relative_filepath = path.relative_to(self.root_dir).as_posix()
            
                    # Check if file matches any content-only exclusion patterns
                    for pattern in Config.CONTENT_ONLY_EXCLUDED_PATTERNS:
                        if fnmatch.fnmatch(path.name, pattern):
                            self.exclusion_counters['content_only'] += 1
                            self.content_only_excluded_files.add(path)
                            return True
            
                    # Check content-only excluded regex patterns
                    for pattern in Config.CONTENT_ONLY_EXCLUDED_REGEX:
                        if re.match(pattern, relative_filepath):
                            self.exclusion_counters['content_only'] += 1
                            self.content_only_excluded_files.add(path)
                            return True
            
                    return False
            
                def is_excluded(self, path: Path) -> bool:
                    """
                    Check if a path should be completely excluded from both structure and content.
                    Returns True if the path should be completely excluded.
                    """
                    relative_filepath = path.relative_to(self.root_dir).as_posix()
            
                    # Check if any part of the path is in EXCLUDED_DIRS
                    for part in path.parts:
                        if part in Config.EXCLUDED_DIRS + self.exclude_dir_patterns:
                            if path.is_dir():
                                self.exclusion_counters['dirs'] += 1
                            else:
                                self.exclusion_counters['patterns'] += 1
                            return True
            
                    # Check excluded regex patterns
                    for pattern in Config.EXCLUDED_REGEX + self.exclude_regex:
                        if re.match(pattern, relative_filepath):
                            self.exclusion_counters['regex'] += 1
                            return True
            
                    # Check gitignore patterns
                    for pattern in self.gitignore_patterns:
                        if fnmatch.fnmatch(relative_filepath, pattern):
                            self.exclusion_counters['gitignore'] += 1
                            return True
            
                    # Check excluded file patterns
                    if path.is_file():
                        for pattern in Config.EXCLUDED_PATTERNS + self.exclude_file_patterns:
                            if fnmatch.fnmatch(path.name, pattern):
                                self.exclusion_counters['patterns'] += 1
                                return True
            
                    return False
            
            class MarkdownGenerator:
            
                # Markdown Generation Logic
                # =======================================================
                def __init__(self, root_dir: Path, output_file: Path, max_depth=None, extensions=None,
                             include_empty_dirs=False, exclude_dir_patterns=None, exclude_file_patterns=None,
                             include_all_files=False, show_all_files_in_filestructure=False, use_gitignore=False,
                             files_first=False):
                    self.root_dir = root_dir
                    self.output_file = output_file
                    self.max_depth = max_depth
                    self.extensions = self.resolve_extensions(extensions)
                    self.include_empty_dirs = include_empty_dirs
                    self.exclude_dir_patterns = exclude_dir_patterns
                    self.exclude_file_patterns = exclude_file_patterns
                    self.include_all_files = include_all_files
                    self.show_all_files_in_filestructure = show_all_files_in_filestructure
                    self.use_gitignore = use_gitignore
                    self.files_first = files_first
                    self.excluder = FileExcluder(
                        root_dir=self.root_dir,
                        exclude_dir_patterns=self.exclude_dir_patterns,
                        exclude_file_patterns=self.exclude_file_patterns,
                        use_gitignore=self.use_gitignore
                    )
                    self.console = Console()
            
                def resolve_extensions(self, extensions_list):
                    logger.debug(f"Resolving extensions: {extensions_list}")
                    resolved = []
                    for item in extensions_list:
                        if item in Config.EXTENSION_GROUPS:
                            resolved.extend(Config.EXTENSION_GROUPS[item])
                            logger.debug(f"Group '{item}' -> {Config.EXTENSION_GROUPS[item]}")
                        else:
                            resolved.append(item)
                            logger.debug(f"Extension '{item}' added")
                    logger.debug(f"Final extensions: {resolved}")
                    return resolved
            
                def get_code_block_type(self, ext):
                    return Config.CODE_BLOCK_TYPES.get(ext, ext)
            
                def generate_markdown_for_file(self, file_path: Path):
                    relative_filepath = file_path.relative_to(self.root_dir)
                    ext = file_path.suffix[1:]
                    encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'utf-16', 'utf-16-le', 'utf-16-be']
                    content = None
            
                    try:
                        with open(file_path, 'rb') as f:
                            raw = f.read(10000)
                        detected = chardet.detect(raw)['encoding']
                        if detected:
                            encodings.insert(0, detected)
                    except Exception as e:
                        logger.error(f"Encoding detection error for {file_path}: {e}")
            
                    for enc in encodings:
                        try:
                            with open(file_path, 'r', encoding=enc) as f:
                                content = f.read()
                            break
                        except UnicodeDecodeError:
                            continue
                        except Exception as e:
                            logger.error(f"Error reading {file_path} with {enc}: {e}")
            
                    if content is None:
                        return f"#### `{relative_filepath}`\n\nError: Unable to read file.\n\n"
            
                    # Current file header
                    file_header = f"\n\n---\n\n#### `{relative_filepath}`\n\n"
            
                    # Determine block type based on file extension
                    block_type = self.get_code_block_type(ext)
                    block_prefix = f"```{block_type}\n" if block_type else ""
                    block_suffix = "\n```" if block_type else ""
            
                    # Indent content by 4 spaces and combine elements
                    lines = content.splitlines()
                    indented_content = '\n'.join(f"    {line}" for line in lines)
                    content_entry = f"{file_header}{block_prefix}{indented_content}{block_suffix}\n\n"
            
                    return content_entry
            
                def build_tree(self, paths):
                    tree = {}
                    for p in paths:
                        parts = p.relative_to(self.root_dir).parts
                        node = tree
                        for part in parts[:-1]:
                            node = node.setdefault(part, {})
                        node.setdefault(parts[-1], {})
                    return tree
            
                def is_content_excluded_path(self, path):
                    """Check if a path is in the content-excluded files set"""
                    return path in self.excluder.content_only_excluded_files
            
                def print_tree(self, node, prefix="", current_path=None, show_all_files=False):
                    lines = []
                    keys = list(node.keys())
            
                    if current_path is None:
                        current_path = self.root_dir
            
                    # Separate files and directories
                    files = [k for k in keys if not node[k]]  # Empty dict means it's a file
                    dirs = [k for k in keys if node[k]]       # Non-empty dict means it's a directory
            
                    # Sort files and directories separately
                    files.sort()
                    dirs.sort()
            
                    # Combine files and directories based on files_first parameter
                    sorted_keys = files + dirs if self.files_first else dirs + files
            
                    for i, key in enumerate(sorted_keys):
                        connector = "└──" if i == len(sorted_keys)-1 else "├──"
            
                        # For files, determine the appropriate marker
                        if key in files:
                            path = current_path / key
                            if show_all_files:
                                # When showing all files, mark excluded files with [-] and included files with [ ]
                                if self.excluder.is_excluded(path):
                                    lines.append(f"{prefix}{connector} {Config.CONTENT_EXCLUDED_MARKER} {key}")
                                else:
                                    lines.append(f"{prefix}{connector} {Config.CONTENT_INCLUDED_MARKER} {key}")
                            else:
                                # Original behavior: only show content-excluded marker
                                if self.excluder.is_content_excluded(path):
                                    lines.append(f"{prefix}{connector} {Config.CONTENT_EXCLUDED_MARKER} {key}")
                                else:
                                    lines.append(f"{prefix}{connector} {key}")
                        else:
                            # For directories, no exclusion marker
                            lines.append(f"{prefix}{connector} {key}")
            
                        sub_node = node[key]
                        if sub_node:  # If it's a directory
                            extension = "    " if i == len(sorted_keys)-1 else "│   "
                            next_path = current_path / key
                            lines.extend(self.print_tree(sub_node, prefix=prefix+extension, current_path=next_path, show_all_files=show_all_files))
                    return lines
            
                def generate_markdown(self):
                    try:
                        gitignore_patterns = self.excluder.gitignore_patterns
                        if gitignore_patterns:
                            self.exclude_dir_patterns = (self.exclude_dir_patterns or []) + gitignore_patterns
                            self.exclude_file_patterns = (self.exclude_file_patterns or []) + gitignore_patterns
            
                        logger.debug(f"Generating markdown for {self.root_dir} -> {self.output_file}")
                        logger.debug(f"Extensions: {self.extensions}, Include all: {self.include_all_files}, Show all in structure: {self.show_all_files_in_filestructure}")
            
                        structure_patterns = ["*"] if self.include_all_files or self.show_all_files_in_filestructure else [f"*.{ext}" for ext in self.extensions]
                        content_patterns = ["*"] if self.include_all_files else [f"*.{ext}" for ext in self.extensions]
            
                        markdown_content = f"# Dir `{self.root_dir.stem}`\n\n"
                        # Show marker explanation when showing all files in structure
                        if self.show_all_files_in_filestructure:
                            markdown_content += f"*Files marked `{Config.CONTENT_EXCLUDED_MARKER}` are excluded from content, `{Config.CONTENT_INCLUDED_MARKER}` files are included.*\n\n"
            
                        excluded = []
                        processed = []
                        counters = self.excluder.exclusion_counters
            
                        with Progress(
                            SpinnerColumn(),
                            TextColumn("[progress.description]{task.description}"),
                            BarColumn(),
                            MofNCompleteColumn(),
                            TimeElapsedColumn(),
                            console=self.console,
                        ) as progress:
                            paths = sorted(self.root_dir.rglob("*"))
                            total = len(paths)
                            logger.debug(f"Found {total} paths.")
                            task1 = progress.add_task("[cyan]Gathering file structure...", total=total)
            
                            included_paths = []
                            all_structure_paths = []  # For tracking all paths that should appear in structure
                            
                            for path in paths:
                                if self.excluder.is_excluded(path):
                                    excluded.append(path)
                                    # When showing all files in structure, include excluded files too
                                    if self.show_all_files_in_filestructure:
                                        all_structure_paths.append(path)
                                else:
                                    if ((self.max_depth is None or len(path.relative_to(self.root_dir).parts) <= self.max_depth)
                                        and (path.is_dir() or any(fnmatch.fnmatch(path.name, pat) for pat in structure_patterns))
                                        and (self.include_empty_dirs or not path.is_dir() or
                                             any(
                                                 not self.excluder.is_excluded(f) and f.is_file() and any(fnmatch.fnmatch(f.name, p) for p in structure_patterns)
                                                 for f in path.rglob("*")
                                             ))
                                       ):
                                        included_paths.append(path)
                                        all_structure_paths.append(path)
                                progress.update(task1, advance=1)
            
                            # Use all_structure_paths when showing all files, otherwise use included_paths
                            structure_paths = all_structure_paths if self.show_all_files_in_filestructure else included_paths
                            tree = self.build_tree(structure_paths)
                            file_structure_lines = self.print_tree(tree, show_all_files=self.show_all_files_in_filestructure)
                            file_structure_str = "### File Structure\n\n```\n" + "\n".join(file_structure_lines) + "\n```\n"
                            markdown_content += file_structure_str
            
                            progress.update(task1, completed=total)
                            logger.debug(f"Excluded {len(excluded)} paths: {counters}")
            
                            # Files to process for content (excluding content-only excluded files)
                            files = [p for p in paths if p not in excluded and p.is_file()
                                     and not self.excluder.is_content_excluded(p)
                                     and (self.max_depth is None or len(p.relative_to(self.root_dir).parts) <= self.max_depth)
                                     and any(fnmatch.fnmatch(p.name, pat) for pat in content_patterns)]
            
                            # Sort files based on files_first parameter
                            def sort_key(path):
                                parts = path.relative_to(self.root_dir).parts
                                if self.files_first:
                                    # For files-first, we want files in the current directory to come before subdirectories
                                    return (len(parts), 0 if len(parts) == 1 else 1) + parts
                                else:
                                    # For directories-first, we want files in subdirectories to be grouped with their directories
                                    return parts
            
                            files.sort(key=sort_key)
                            logger.debug(f"{len(files)} files to process.")
                            task2 = progress.add_task("[cyan]Processing files for content...", total=len(files))
            
                            for idx, file in enumerate(files, 1):
                                try:
                                    content = self.generate_markdown_for_file(file)
                                    markdown_content += f"{content}"
                                    processed.append(file)
                                except Exception as e:
                                    logger.error(f"Failed on {file}: {e}")
                                finally:
                                    progress.update(task2, advance=1)
            
                            logger.debug(f"Processed {len(processed)} files.")
            
                        # Removes consecutive blank lines (preserving at most one blank line between text lines).
                        markdown_content_clean = re.sub(r"\n{2,}", "\n\n", markdown_content)
                        self.output_file.write_text(markdown_content_clean, encoding="utf-8")
                        logger.info(f"Markdown generated at {self.output_file}")
            
                    except Exception as e:
                        logger.error(f"Markdown generation failed: {e}")
                        raise
            
            class MarkdownGeneratorApp:
            
                # Main Application Logic
                # =======================================================
                def __init__(self):
                    LoggerSetup.initialize_logging()
                    self.arg_handler = ArgumentHandler()
            
                @staticmethod
                def ensure_md_extension(filename):
                    return f"{filename}.md" if not filename.endswith('.md') else filename
            
                @staticmethod
                def ensure_directory_exists(directory: Path):
                    directory.mkdir(parents=True, exist_ok=True)
                    logger.info(f"Created output directory: {directory}")
            
                @staticmethod
                def clear_console():
                    os.system("cls" if platform.system() == "Windows" else "clear")
            
                @staticmethod
                def display_summary(console, args):
                    table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
                    table.add_column("Parameter", style="dim", width=30)
                    table.add_column("Value", style="bold cyan")
            
                    summary_data = [
                        ("Input directory path", str(args.input_path)),
                        ("Output markdown file path", str(args.output_path)),
                        ("Output filename", str(args.output_filename)),
                        ("Maximum directory depth", str(args.depth)),
                        ("Include all files", "Yes" if args.include_all_files else "No"),
                        ("Show all files in filestructure", "Yes" if args.show_all_files_in_filestructure else "No"),
                        ("Include empty directories", "Yes" if args.include_empty_dirs else "No"),
                        ("Files before directories", "Yes" if args.files_first else "No"),
                        ("Excluded directory patterns", ', '.join(args.exclude_dir_patterns) if args.exclude_dir_patterns else "None"),
                        ("Excluded file patterns", ', '.join(args.exclude_file_patterns) if args.exclude_file_patterns else "None"),
                        ("Use .gitignore for exclusions", "Yes" if args.use_gitignore else "No"),
                        ("Cleanup log files after success", "Yes" if args.cleanup_logs else "No"),
                    ]
            
                    if not args.include_all_files:
                        summary_data.insert(6, ("File extensions", ', '.join(args.extensions)))
            
                    for param, value in summary_data:
                        table.add_row(param, value)
            
                    console.print(table)
            
                def run(self):
                    logger.debug("Main started.")
                    args = self.arg_handler.get_arguments()
                    logger.debug(f"Arguments: {args}")
            
                    # Get user inputs using the new pattern
                    args = self.arg_handler.get_user_inputs(args)
                    logger.debug(f"Post-input arguments: {args}")
            
                    if not args.input_path or not args.output_path:
                        Console().print("[bold red]Error: Input and output directories required.[/bold red]")
                        logger.error("Input and output directories required.")
                        return
            
                    input_path = Path(args.input_path)
                    output_path = Path(args.output_path)
                    output_filename = self.ensure_md_extension(args.output_filename) if args.output_filename else "py__MarkdownGenerator.md"
                    full_output = output_path / output_filename
            
                    logger.debug(f"Input: {input_path}, Output: {output_path}, Filename: {output_filename}")
            
                    self.ensure_directory_exists(output_path)
                    self.display_summary(Console(), args)
            
                    try:
                        generator = MarkdownGenerator(
                            root_dir=input_path,
                            output_file=full_output,
                            max_depth=args.depth,
                            extensions=args.extensions,
                            include_empty_dirs=args.include_empty_dirs,
                            exclude_dir_patterns=args.exclude_dir_patterns,
                            exclude_file_patterns=args.exclude_file_patterns,
                            include_all_files=args.include_all_files,
                            show_all_files_in_filestructure=args.show_all_files_in_filestructure,
                            use_gitignore=args.use_gitignore,
                            files_first=args.files_first,
                        )
                        generator.generate_markdown()
                        Console().print(f"\nMarkdown generated at [bold cyan]{full_output}[/bold cyan]\n")
            
                        # Cleanup log file if specified and successful execution
                        if args.cleanup_logs:
                            self.cleanup_logs()
            
                    except Exception as e:
                        logger.error(f"Generation failed: {e}")
                        Console().print(f"\n[bold red]Error:[/bold red] {e}\n")
                        raise
            
                def cleanup_logs(self):
                    """Clean up log files after successful execution"""
                    logger.remove()
                    log_file = Path("app.log.yml")
                    if log_file.exists():
                        try:
                            log_file.unlink()
                            Console().print(f"Log file [bold green]{log_file}[/bold green] has been cleaned up.\n")
                        except Exception as e:
                            Console().print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")
                    else:
                        Console().print(f"No log file found to clean up at {log_file}\n")
            
            def wait_for_user_exit():
                """Wait for user to press any key before exiting"""
                console = Console()
                console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
                try:
                    input()
                except KeyboardInterrupt:
                    pass
            
            def main():
                try:
                    app = MarkdownGeneratorApp()
                    app.run()
                except Exception as e:
                    console = Console()
                    console.print(f"\n[bold red]Error:[/bold red] {e}")
                    logger.exception(f"Main execution error: {e}")
                finally:
                    console = Console()
                    console.print("\n[bold green]Finished processing.[/bold green]")
                    wait_for_user_exit()
            
            if __name__ == "__main__":
                main()
        ```
        
    ```
    
    ---
    
    #### `py__MarkdownGenerator.sublime-project`
    
    ```sublime-project
        {
            "folders": [
                {
                    "path": ".",
                    "folder_exclude_patterns": [
                        "__pycache__",
                        // "logs",
                        "env",
                        "venv",
                        ".git",
                        ".svn",
                        ".hg",
                        ".idea",
                        ".vscode",
                        "node_modules",
                        "*.egg-info",
                        "dist",
                        "build",
                        ".DS_Store"
                    ],
                    "file_exclude_patterns": [
                        "*.pyc",
                        "*.pyo",
                        // "*.log",
                        "*.tmp",
                        "*.sublime-workspace",
                        ".DS_Store",
                        "*.swp"
                    ]
                }
            ],
            "settings": {
                "tab_size": 4,
                "default_line_ending": "unix",
                "translate_tabs_to_spaces": true,
                "ensure_newline_at_eof_on_save": true,
                "trim_trailing_white_space_on_save": true,
                "python_interpreter": "$project_path/venv/Scripts/python",
                "python_formatter": "black",
                "python_linter": "flake8",
                "python_format_on_save": false
            },
            "build_systems": [
                {
                    "name": "py__MarkdownGenerator.sublime-project",
                    "cmd": [
                        "$project_path/venv/Scripts/python",
                        "-u",
                        "${file}"
                    ],
                    "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                    "selector": "source.python",
                    "shell": true,
                    "working_dir": "${project_path}"
                }
            ]
        }
    ```
    
    ---
    
    #### `pyproject.toml`
    
    ```toml
        [project]
        name = "markdown-generator"
        version = "1.0.0"
        description = "Generate comprehensive Markdown documentation from file structures with rich CLI interface"
        requires-python = ">=3.9"
        
        dependencies = [
            "chardet",
            "gitignore-parser",
            "loguru",
            "PyYAML",
            "rich",
        ]
    ```
    
    ---
    
    #### `run.bat`
    
    ```batch
        @ECHO OFF
        SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
        :: =============================================================================
        :: Markdown Generator - Universal Runner
        :: =============================================================================
        IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
        
        :: Check for uv installation
        WHERE uv >nul 2>&1
        IF ERRORLEVEL 1 (
            ECHO [ERROR] uv is not installed or not in PATH
            ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
            ECHO.
            ECHO Falling back to traditional Python execution...
            python src\main.py --prompt
            GOTO :End
        )
        
        :: Check for pyproject.toml
        IF NOT EXIST "pyproject.toml" (
            ECHO [ERROR] pyproject.toml not found
            ECHO Please ensure the project is properly configured
            PAUSE>NUL & EXIT /B
        )
        
        :: Initialize environment if needed
        IF NOT EXIST ".venv" (
            ECHO [INFO] Initializing uv environment...
            uv sync
            IF ERRORLEVEL 1 (
                ECHO [ERROR] Failed to initialize environment
                PAUSE>NUL & EXIT /B
            )
            ECHO [SUCCESS] Environment initialized
            ECHO.
        )
        
        :: Run the application
        ECHO [INFO] Starting Markdown Generator...
        uv run python src\main.py --prompt
        
        :End
        ECHO.
        ECHO Press any key to restart or Ctrl+C to exit...
        PAUSE >NUL
        CLS
        GOTO :EOF
    ```
    
    ---
    
    #### `src\main.py`
    
    ```python
        import argparse
        import fnmatch
        import os
        import platform
        import re
        import sys
        from pathlib import Path
        
        import chardet
        import yaml
        from gitignore_parser import parse_gitignore
        from loguru import logger
        from rich import box
        from rich.console import Console
        from rich.prompt import Confirm, Prompt
        from rich.progress import (
            Progress,
            SpinnerColumn,
            TimeElapsedColumn,
            TextColumn,
            BarColumn,
            MofNCompleteColumn,
        )
        from rich.table import Table
        
        class Config:
        
            # Configuration Constants
            # =======================================================
            DEFAULT_DEPTH = 99
            DEFAULT_EXTENSIONS = ["py"]
            DEFAULT_INCLUDE_ALL_FILES = True
            DEFAULT_PREDEFINED_EXTENSION_GROUPS = False
            DEFAULT_FULL_STRUCTURE_PREVIEW = False
            DEFAULT_CLEANUP_LOGS = True
            DEFAULT_FILES_FIRST = True
        
            # Directory exclusions - always exclude completely
            EXCLUDED_DIRS = [
                ".backups", ".github", ".git", ".gpt", ".ignore",
                ".old", ".cmd", ".tmp", ".venv", ".versions", ".specstory",
                "__meta__", "__pycache__", "__tmp__", "venv", "node_modules",
            ]
        
            # Complete exclusions (excluded from both structure and content)
            EXCLUDED_PATTERNS = [
                # Compiled/bytecode files
                "*.pyc", "*.pyo", "*.class", 
                # Database files
                "*.db", 
                # Binary/executable files
                "*.exe", "*.dll", "*.so", "*.dylib", "*.bin",
                # Log files
                "*.log", "*.log.yml",  "uv.lock",
                # Special files
                ".new_hashes.py", ".original_hashes.py",
                # Commented out but kept for reference
                # "*.bat"
            ]
        
            # Regex patterns for complete exclusion
            EXCLUDED_REGEX = [r".*\.tmp$"]
        
            # Content-only exclusions (show in structure but exclude from content)
            CONTENT_ONLY_EXCLUDED_PATTERNS = [
                # Image files
                "*.ico", "*.png", "*.jpg", "*.jpeg", "*.heic", "*.gif", "*.bmp", "*.svg",
                # Audio files
                "*.mp3", "*.wav", "*.ogg", "*.flac", "*.aac",
                # Video files
                "*.mp4", "*.avi", "*.mov", "*.mkv", "*.webm", "*.m4a",
                # Document files
                "*.pdf", "*.docx", "*.xlsx", "*.pptx",
                # Font files
                "*.ttf", "*.otf", "*.woff", "*.woff2",
                # Archive files
                "*.zip", "*.tar", "*.gz", "*.rar", "*.7z",
                # IDE files
                "*.sublime-workspace"
            ]
            
            # Regex patterns for content-only exclusion
            CONTENT_ONLY_EXCLUDED_REGEX = []
        
            # Default markers for visual indicators
            CONTENT_EXCLUDED_MARKER = "[-]"  # Marker for files in structure but excluded from content
            CONTENT_INCLUDED_MARKER = "[ ]"  # Marker for files in structure and included in content
        
            EXTENSION_GROUPS = {
                # "SublimeText": ["py", "*sublime*", "tmLanguage", "tmPreferences", "tmTheme", "stTheme"],
                "SublimeText": ["py", "sublime-commands", "sublime-keymap", "sublime-settings", "sublime-menu"],
                "Python": ["*pdm*", "env", "py", "pyi", "pyo", "toml", "jinja*"],
                "bat|py|txt": ["bat", "py", "txt"],
                "Web:React": ["ts", "tsx", "js", "json", "cjs", "css", "html", ],
                # "Web": ["css", "html", "js"],
                # "Data": ["cfg", "csv", "json", "xml"],
            }
        
            CODE_BLOCK_TYPES = {
                "py": "python", "json": "json", "nss": "java", "log": "text", "txt": "text",
                "md": "markdown", "html": "html", "htm": "html", "css": "css", "js": "javascript",
                "ts": "typescript", "xml": "xml", "yaml": "yaml", "yml": "yaml", "sh": "bash",
                "bat": "batch", "ini": "ini", "cfg": "ini", "java": "java", "c": "c", "cpp": "cpp",
                "h": "cpp", "hpp": "cpp", "cs": "csharp", "go": "go", "rb": "ruby", "php": "php",
                "sql": "sql", "swift": "swift", "kt": "kotlin", "rs": "rust", "r": "r", "pl": "perl",
                "lua": "lua", "scala": "scala", "vb": "vbnet",
            }
        
        class LoggerSetup:
        
            # Logging
            # =======================================================
            @staticmethod
            def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
                def yaml_sink(message):
                    record = message.record
        
                    time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"
                    level_str = f"!{record['level'].name}"
                    name_str = record['name']
                    funcName_str = f"*{record['function']}"
                    lineno_int = record["line"]
                    msg = record["message"]
        
                    if "\n" in msg:
                        lines = msg.split("\n")
                        message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
                    else:
                        # Quote message if it has special characters
                        if ":" in msg:
                            message_str = f"'{msg}'"
                        else:
                            message_str = msg
        
                    yaml_lines = [
                        f"- time: {time_str}",
                        f"  level: {level_str}",
                        f"  name: {name_str}",
                        f"  funcName: {funcName_str}",
                        f"  lineno: {lineno_int}",
                        f"  message: {message_str}",
                        ""
                    ]
        
                    with open(log_file, "a", encoding="utf-8") as f:
                        f.write("\n".join(yaml_lines) + "\n")
        
                logger.remove()
                logger.add(yaml_sink, level=level, enqueue=True)
        
            @staticmethod
            def initialize_logging():
                LoggerSetup.setup_yaml_logging()
        
        class ArgumentHandler:
        
            # Argument Parsing and Prompting
            # =======================================================
            def __init__(self):
                self.parser = self.parse_arguments()
        
            @staticmethod
            def parse_arguments():
                logger.debug("Setting up argument parser.")
                parser = argparse.ArgumentParser(description="Generate Markdown documentation from a file structure.")
                parser.add_argument('-i', '--input_path', type=str, help="Input directory path")
                parser.add_argument('-op', '--output_path', type=str, help="Output directory path")
                parser.add_argument('-of', '--output_filename', type=str, help="Output markdown filename")
                parser.add_argument('-d', '--depth', type=int, help="Max directory depth", default=Config.DEFAULT_DEPTH)
                parser.add_argument('--include_all_files', action='store_true', help="Include all file types", default=Config.DEFAULT_INCLUDE_ALL_FILES)
                parser.add_argument('-e', '--extensions', nargs='+', help="File extensions/groups", default=Config.DEFAULT_EXTENSIONS)
                parser.add_argument('--use-gitignore', action='store_true', help="Use .gitignore for exclusions", default=False)
                parser.add_argument('-edp', '--exclude_dir_patterns', nargs='+', help="Excluded directory patterns")
                parser.add_argument('-efp', '--exclude_file_patterns', nargs='+', help="Excluded file patterns")
                parser.add_argument('--show_all_files_in_filestructure', action='store_true', default=Config.DEFAULT_FULL_STRUCTURE_PREVIEW, help="Show all files in structure")
                parser.add_argument('--include_empty_dirs', action='store_true', help="Include empty directories")
                parser.add_argument('--files_first', action='store_true', default=Config.DEFAULT_FILES_FIRST, help="List files before directories in the output")
                parser.add_argument('--prompt', action='store_true', help="Prompt for input values")
                parser.add_argument('--log_to_current_dir', action='store_true', help="Log to current dir")
        
                # Arguments for Log Cleanup
                cleanup_logs_group = parser.add_mutually_exclusive_group()
                cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true', help="Clean up log files after successful execution")
                cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false', help="Do not clean up log files after successful execution")
                parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)
        
                logger.debug("Argument parser setup complete.")
                return parser
        
            def get_arguments(self):
                return self.parser.parse_args()
        
            def get_user_inputs(self, args):
                """Get user inputs following the pattern from main.old.py"""
                logger.debug("Getting user inputs.")
                console = Console()
        
                def print_section(title):
                    console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)
        
                # If --prompt is used, ask for all configuration options
                if args.prompt:
                    # Default Settings prompt comes first, following main.old.py pattern
                    print_section("Default Settings")
                    use_defaults = Confirm.ask("Use default settings?", default=True)
                    logger.debug(f"Use defaults: {use_defaults}")
        
                    if not use_defaults:
                        # --- File Types ---
                        print_section("File Types")
                        args.include_all_files = Confirm.ask("Include all file types?", default=args.include_all_files)
        
                        if not args.include_all_files:
                            if Confirm.ask("Use predefined extension group?", default=Config.DEFAULT_PREDEFINED_EXTENSION_GROUPS):
                                group_names = list(Config.EXTENSION_GROUPS.keys())
                                table = Table(header_style="bold magenta", box=box.SIMPLE)
                                table.add_column("No.", style="bold cyan", justify="right")
                                table.add_column("Group Name", style="bold cyan")
                                table.add_column("Extensions", style="magenta")
                                for i, group in enumerate(group_names, 1):
                                    exts = ', '.join(Config.EXTENSION_GROUPS[group])
                                    table.add_row(str(i), group, exts)
                                console.print(table)
                                choice = Prompt.ask("Choose group by number:", choices=[str(i) for i in range(1, len(group_names) + 1)])
                                selected_group = group_names[int(choice) - 1]
                                args.extensions = Config.EXTENSION_GROUPS[selected_group]
                            else:
                                exts_input = Prompt.ask("File extensions (space-separated):", default=' '.join(args.extensions or Config.DEFAULT_EXTENSIONS))
                                args.extensions = exts_input.split()
        
                        # --- Optional Settings ---
                        print_section("Optional Settings")
                        depth_input = Prompt.ask("Max depth:", default=str(args.depth))
                        args.depth = int(depth_input) if depth_input.isdigit() else Config.DEFAULT_DEPTH
        
                        if not args.include_all_files:
                            args.show_all_files_in_filestructure = Confirm.ask("Include unmatched files?", default=args.show_all_files_in_filestructure)
        
                        args.include_empty_dirs = Confirm.ask("Include empty dirs?", default=args.include_empty_dirs)
                        args.files_first = Confirm.ask("List files before directories?", default=args.files_first)
        
                        # --- Exclusions ---
                        print_section("Exclusions")
                        args.use_gitignore = Confirm.ask("Use .gitignore?", default=args.use_gitignore)
                        excl_dir = Prompt.ask("Exclude folders:", default=' '.join(args.exclude_dir_patterns or []))
                        args.exclude_dir_patterns = excl_dir.split() if excl_dir else []
                        excl_file = Prompt.ask("Exclude files:", default=' '.join(args.exclude_file_patterns or []))
                        args.exclude_file_patterns = excl_file.split() if excl_file else []
        
                        # --- Input/Output ---
                        print_section("Input/Output")
                        if not args.input_path:
                            args.input_path = Prompt.ask("Input folder:", default=args.input_path)
                        if not args.output_path:
                            args.output_path = Prompt.ask("Output folder:", default=args.output_path)
                        if not args.output_filename:
                            args.output_filename = Prompt.ask("Output file (.md):", default=args.output_filename or 'py__MarkdownGenerator.md')
        
                        # --- Cleanup Logs ---
                        args.cleanup_logs = Confirm.ask("Clean up log files after successful execution?", default=args.cleanup_logs)
                    else:
                        # Assign defaults if using default settings
                        args.input_path = args.input_path or ""
                        args.output_path = args.output_path or ""
                        args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
                        args.depth = args.depth or Config.DEFAULT_DEPTH
                        args.include_all_files = args.include_all_files or True
                        args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
                        args.use_gitignore = args.use_gitignore or False
                        args.exclude_dir_patterns = args.exclude_dir_patterns or []
                        args.exclude_file_patterns = args.exclude_file_patterns or []
                        args.show_all_files_in_filestructure = args.show_all_files_in_filestructure or Config.DEFAULT_FULL_STRUCTURE_PREVIEW
                        args.include_empty_dirs = args.include_empty_dirs or False
                        args.files_first = args.files_first or Config.DEFAULT_FILES_FIRST
                        args.cleanup_logs = args.cleanup_logs
        
                # Ensure defaults for any unset values
                args.input_path = args.input_path or os.getcwd()
                args.output_path = args.output_path or os.getcwd()
                args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
                args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
                args.exclude_dir_patterns = args.exclude_dir_patterns or []
                args.exclude_file_patterns = args.exclude_file_patterns or []
        
                logger.debug("User input collection complete.")
                return args
        
        class FileExcluder:
        
            # File Exclusion Logic
            # =======================================================
            def __init__(self, root_dir: Path, exclude_dir_patterns=None, exclude_file_patterns=None,
                         exclude_regex=None, use_gitignore=False):
                self.root_dir = root_dir
                self.exclude_dir_patterns = exclude_dir_patterns or []
                self.exclude_file_patterns = exclude_file_patterns or []
                self.exclude_regex = exclude_regex or []
                self.gitignore_patterns = self.load_gitignore_patterns() if use_gitignore else []
                self.exclusion_counters = {"dirs": 0, "patterns": 0, "regex": 0, "gitignore": 0, "content_only": 0}
                # Track files that should be excluded from content but shown in structure
                self.content_only_excluded_files = set()
        
            def load_gitignore_patterns(self):
                gitignore_path = self.root_dir / '.gitignore'
                if not gitignore_path.exists():
                    logger.debug(f"No .gitignore found at {gitignore_path}")
                    return []
        
                with open(gitignore_path, 'r') as file:
                    lines = file.readlines()
        
                patterns = []
                for line in lines:
                    stripped = line.strip()
                    if stripped and not stripped.startswith('#'):
                        if stripped.endswith('/'):
                            stripped = stripped.rstrip('/') + '/**'
                        elif stripped.startswith('/'):
                            stripped = stripped.lstrip('/')
                        patterns.append(stripped)
                logger.debug(f".gitignore patterns: {patterns}")
                return patterns
        
            def is_content_excluded(self, path: Path) -> bool:
                """
                Check if a file should be excluded from content but shown in structure.
                Returns True if the file should be excluded from content only.
                """
                if path.is_dir():
                    return False  # Directories are never content-excluded
        
                relative_filepath = path.relative_to(self.root_dir).as_posix()
        
                # Check if file matches any content-only exclusion patterns
                for pattern in Config.CONTENT_ONLY_EXCLUDED_PATTERNS:
                    if fnmatch.fnmatch(path.name, pattern):
                        self.exclusion_counters['content_only'] += 1
                        self.content_only_excluded_files.add(path)
                        return True
        
                # Check content-only excluded regex patterns
                for pattern in Config.CONTENT_ONLY_EXCLUDED_REGEX:
                    if re.match(pattern, relative_filepath):
                        self.exclusion_counters['content_only'] += 1
                        self.content_only_excluded_files.add(path)
                        return True
        
                return False
        
            def is_excluded(self, path: Path) -> bool:
                """
                Check if a path should be completely excluded from both structure and content.
                Returns True if the path should be completely excluded.
                """
                relative_filepath = path.relative_to(self.root_dir).as_posix()
        
                # Check if any part of the path is in EXCLUDED_DIRS
                for part in path.parts:
                    if part in Config.EXCLUDED_DIRS + self.exclude_dir_patterns:
                        if path.is_dir():
                            self.exclusion_counters['dirs'] += 1
                        else:
                            self.exclusion_counters['patterns'] += 1
                        return True
        
                # Check excluded regex patterns
                for pattern in Config.EXCLUDED_REGEX + self.exclude_regex:
                    if re.match(pattern, relative_filepath):
                        self.exclusion_counters['regex'] += 1
                        return True
        
                # Check gitignore patterns
                for pattern in self.gitignore_patterns:
                    if fnmatch.fnmatch(relative_filepath, pattern):
                        self.exclusion_counters['gitignore'] += 1
                        return True
        
                # Check excluded file patterns
                if path.is_file():
                    for pattern in Config.EXCLUDED_PATTERNS + self.exclude_file_patterns:
                        if fnmatch.fnmatch(path.name, pattern):
                            self.exclusion_counters['patterns'] += 1
                            return True
        
                return False
        
        class MarkdownGenerator:
        
            # Markdown Generation Logic
            # =======================================================
            def __init__(self, root_dir: Path, output_file: Path, max_depth=None, extensions=None,
                         include_empty_dirs=False, exclude_dir_patterns=None, exclude_file_patterns=None,
                         include_all_files=False, show_all_files_in_filestructure=False, use_gitignore=False,
                         files_first=False):
                self.root_dir = root_dir
                self.output_file = output_file
                self.max_depth = max_depth
                self.extensions = self.resolve_extensions(extensions)
                self.include_empty_dirs = include_empty_dirs
                self.exclude_dir_patterns = exclude_dir_patterns
                self.exclude_file_patterns = exclude_file_patterns
                self.include_all_files = include_all_files
                self.show_all_files_in_filestructure = show_all_files_in_filestructure
                self.use_gitignore = use_gitignore
                self.files_first = files_first
                self.excluder = FileExcluder(
                    root_dir=self.root_dir,
                    exclude_dir_patterns=self.exclude_dir_patterns,
                    exclude_file_patterns=self.exclude_file_patterns,
                    use_gitignore=self.use_gitignore
                )
                self.console = Console()
        
            def resolve_extensions(self, extensions_list):
                logger.debug(f"Resolving extensions: {extensions_list}")
                resolved = []
                for item in extensions_list:
                    if item in Config.EXTENSION_GROUPS:
                        resolved.extend(Config.EXTENSION_GROUPS[item])
                        logger.debug(f"Group '{item}' -> {Config.EXTENSION_GROUPS[item]}")
                    else:
                        resolved.append(item)
                        logger.debug(f"Extension '{item}' added")
                logger.debug(f"Final extensions: {resolved}")
                return resolved
        
            def get_code_block_type(self, ext):
                return Config.CODE_BLOCK_TYPES.get(ext, ext)
        
            def generate_markdown_for_file(self, file_path: Path):
                relative_filepath = file_path.relative_to(self.root_dir)
                ext = file_path.suffix[1:]
                encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'utf-16', 'utf-16-le', 'utf-16-be']
                content = None
        
                try:
                    with open(file_path, 'rb') as f:
                        raw = f.read(10000)
                    detected = chardet.detect(raw)['encoding']
                    if detected:
                        encodings.insert(0, detected)
                except Exception as e:
                    logger.error(f"Encoding detection error for {file_path}: {e}")
        
                for enc in encodings:
                    try:
                        with open(file_path, 'r', encoding=enc) as f:
                            content = f.read()
                        break
                    except UnicodeDecodeError:
                        continue
                    except Exception as e:
                        logger.error(f"Error reading {file_path} with {enc}: {e}")
        
                if content is None:
                    return f"#### `{relative_filepath}`\n\nError: Unable to read file.\n\n"
        
                # Current file header
                file_header = f"\n\n---\n\n#### `{relative_filepath}`\n\n"
        
                # Determine block type based on file extension
                block_type = self.get_code_block_type(ext)
                block_prefix = f"```{block_type}\n" if block_type else ""
                block_suffix = "\n```" if block_type else ""
        
                # Indent content by 4 spaces and combine elements
                lines = content.splitlines()
                indented_content = '\n'.join(f"    {line}" for line in lines)
                content_entry = f"{file_header}{block_prefix}{indented_content}{block_suffix}\n\n"
        
                return content_entry
        
            def build_tree(self, paths):
                tree = {}
                for p in paths:
                    parts = p.relative_to(self.root_dir).parts
                    node = tree
                    for part in parts[:-1]:
                        node = node.setdefault(part, {})
                    node.setdefault(parts[-1], {})
                return tree
        
            def is_content_excluded_path(self, path):
                """Check if a path is in the content-excluded files set"""
                return path in self.excluder.content_only_excluded_files
        
            def print_tree(self, node, prefix="", current_path=None, show_all_files=False):
                lines = []
                keys = list(node.keys())
        
                if current_path is None:
                    current_path = self.root_dir
        
                # Separate files and directories
                files = [k for k in keys if not node[k]]  # Empty dict means it's a file
                dirs = [k for k in keys if node[k]]       # Non-empty dict means it's a directory
        
                # Sort files and directories separately
                files.sort()
                dirs.sort()
        
                # Combine files and directories based on files_first parameter
                sorted_keys = files + dirs if self.files_first else dirs + files
        
                for i, key in enumerate(sorted_keys):
                    connector = "└──" if i == len(sorted_keys)-1 else "├──"
        
                    # For files, determine the appropriate marker
                    if key in files:
                        path = current_path / key
                        if show_all_files:
                            # When showing all files, mark excluded files with [-] and included files with [ ]
                            if self.excluder.is_excluded(path):
                                lines.append(f"{prefix}{Config.CONTENT_EXCLUDED_MARKER} {connector} {key}")
                            else:
                                lines.append(f"{prefix}{Config.CONTENT_INCLUDED_MARKER} {connector} {key}")
                        else:
                            # Original behavior: only show content-excluded marker
                            if self.excluder.is_content_excluded(path):
                                lines.append(f"{prefix}{Config.CONTENT_EXCLUDED_MARKER} {connector} {key}")
                            else:
                                lines.append(f"{prefix}{connector} {key}")
                    else:
                        # For directories, determine if they should be marked as excluded
                        if show_all_files:
                            path = current_path / key
                            if self.excluder.is_excluded(path):
                                lines.append(f"{prefix}{Config.CONTENT_EXCLUDED_MARKER} {connector} {key}")
                            else:
                                lines.append(f"{prefix}{Config.CONTENT_INCLUDED_MARKER} {connector} {key}")
                        else:
                            # Original behavior: no exclusion marker for directories
                            lines.append(f"{prefix}{connector} {key}")
        
                    sub_node = node[key]
                    if sub_node:  # If it's a directory
                        extension = "    " if i == len(sorted_keys)-1 else "│   "
                        next_path = current_path / key
                        lines.extend(self.print_tree(sub_node, prefix=prefix+extension, current_path=next_path, show_all_files=show_all_files))
                return lines
        
            def generate_markdown(self):
                try:
                    gitignore_patterns = self.excluder.gitignore_patterns
                    if gitignore_patterns:
                        self.exclude_dir_patterns = (self.exclude_dir_patterns or []) + gitignore_patterns
                        self.exclude_file_patterns = (self.exclude_file_patterns or []) + gitignore_patterns
        
                    logger.debug(f"Generating markdown for {self.root_dir} -> {self.output_file}")
                    logger.debug(f"Extensions: {self.extensions}, Include all: {self.include_all_files}, Show all in structure: {self.show_all_files_in_filestructure}")
        
                    structure_patterns = ["*"] if self.include_all_files or self.show_all_files_in_filestructure else [f"*.{ext}" for ext in self.extensions]
                    content_patterns = ["*"] if self.include_all_files else [f"*.{ext}" for ext in self.extensions]
        
                    markdown_content = f"# Dir `{self.root_dir.stem}`\n\n"
                    # Show marker explanation when showing all files in structure
                    if self.show_all_files_in_filestructure:
                        markdown_content += f"*Files marked `{Config.CONTENT_EXCLUDED_MARKER}` are excluded from content, `{Config.CONTENT_INCLUDED_MARKER}` files are included.*\n\n"
        
                    excluded = []
                    processed = []
                    counters = self.excluder.exclusion_counters
        
                    with Progress(
                        SpinnerColumn(),
                        TextColumn("[progress.description]{task.description}"),
                        BarColumn(),
                        MofNCompleteColumn(),
                        TimeElapsedColumn(),
                        console=self.console,
                    ) as progress:
                        paths = sorted(self.root_dir.rglob("*"))
                        total = len(paths)
                        logger.debug(f"Found {total} paths.")
                        task1 = progress.add_task("[cyan]Gathering file structure...", total=total)
        
                        included_paths = []
                        all_structure_paths = []  # For tracking all paths that should appear in structure
                        
                        for path in paths:
                            if self.excluder.is_excluded(path):
                                excluded.append(path)
                                # When showing all files in structure, include excluded files too
                                if self.show_all_files_in_filestructure:
                                    all_structure_paths.append(path)
                            else:
                                if ((self.max_depth is None or len(path.relative_to(self.root_dir).parts) <= self.max_depth)
                                    and (path.is_dir() or any(fnmatch.fnmatch(path.name, pat) for pat in structure_patterns))
                                    and (self.include_empty_dirs or not path.is_dir() or
                                         any(
                                             not self.excluder.is_excluded(f) and f.is_file() and any(fnmatch.fnmatch(f.name, p) for p in structure_patterns)
                                             for f in path.rglob("*")
                                         ))
                                   ):
                                    included_paths.append(path)
                                    all_structure_paths.append(path)
                            progress.update(task1, advance=1)
        
                        # Use all_structure_paths when showing all files, otherwise use included_paths
                        structure_paths = all_structure_paths if self.show_all_files_in_filestructure else included_paths
                        tree = self.build_tree(structure_paths)
                        file_structure_lines = self.print_tree(tree, show_all_files=self.show_all_files_in_filestructure)
                        file_structure_str = "### File Structure\n\n```\n" + "\n".join(file_structure_lines) + "\n```\n"
                        markdown_content += file_structure_str
        
                        progress.update(task1, completed=total)
                        logger.debug(f"Excluded {len(excluded)} paths: {counters}")
        
                        # Files to process for content (excluding content-only excluded files)
                        files = [p for p in paths if p not in excluded and p.is_file()
                                 and not self.excluder.is_content_excluded(p)
                                 and (self.max_depth is None or len(p.relative_to(self.root_dir).parts) <= self.max_depth)
                                 and any(fnmatch.fnmatch(p.name, pat) for pat in content_patterns)]
        
                        # Sort files based on files_first parameter
                        def sort_key(path):
                            parts = path.relative_to(self.root_dir).parts
                            if self.files_first:
                                # For files-first, we want files in the current directory to come before subdirectories
                                return (len(parts), 0 if len(parts) == 1 else 1) + parts
                            else:
                                # For directories-first, we want files in subdirectories to be grouped with their directories
                                return parts
        
                        files.sort(key=sort_key)
                        logger.debug(f"{len(files)} files to process.")
                        task2 = progress.add_task("[cyan]Processing files for content...", total=len(files))
        
                        for idx, file in enumerate(files, 1):
                            try:
                                content = self.generate_markdown_for_file(file)
                                markdown_content += f"{content}"
                                processed.append(file)
                            except Exception as e:
                                logger.error(f"Failed on {file}: {e}")
                            finally:
                                progress.update(task2, advance=1)
        
                        logger.debug(f"Processed {len(processed)} files.")
        
                    # Removes consecutive blank lines (preserving at most one blank line between text lines).
                    markdown_content_clean = re.sub(r"\n{2,}", "\n\n", markdown_content)
                    self.output_file.write_text(markdown_content_clean, encoding="utf-8")
                    logger.info(f"Markdown generated at {self.output_file}")
        
                except Exception as e:
                    logger.error(f"Markdown generation failed: {e}")
                    raise
        
        class MarkdownGeneratorApp:
        
            # Main Application Logic
            # =======================================================
            def __init__(self):
                LoggerSetup.initialize_logging()
                self.arg_handler = ArgumentHandler()
        
            @staticmethod
            def ensure_md_extension(filename):
                return f"{filename}.md" if not filename.endswith('.md') else filename
        
            @staticmethod
            def ensure_directory_exists(directory: Path):
                directory.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created output directory: {directory}")
        
            @staticmethod
            def clear_console():
                os.system("cls" if platform.system() == "Windows" else "clear")
        
            @staticmethod
            def display_summary(console, args):
                table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
                table.add_column("Parameter", style="dim", width=30)
                table.add_column("Value", style="bold cyan")
        
                summary_data = [
                    ("Input directory path", str(args.input_path)),
                    ("Output markdown file path", str(args.output_path)),
                    ("Output filename", str(args.output_filename)),
                    ("Maximum directory depth", str(args.depth)),
                    ("Include all files", "Yes" if args.include_all_files else "No"),
                    ("Show all files in filestructure", "Yes" if args.show_all_files_in_filestructure else "No"),
                    ("Include empty directories", "Yes" if args.include_empty_dirs else "No"),
                    ("Files before directories", "Yes" if args.files_first else "No"),
                    ("Excluded directory patterns", ', '.join(args.exclude_dir_patterns) if args.exclude_dir_patterns else "None"),
                    ("Excluded file patterns", ', '.join(args.exclude_file_patterns) if args.exclude_file_patterns else "None"),
                    ("Use .gitignore for exclusions", "Yes" if args.use_gitignore else "No"),
                    ("Cleanup log files after success", "Yes" if args.cleanup_logs else "No"),
                ]
        
                if not args.include_all_files:
                    summary_data.insert(6, ("File extensions", ', '.join(args.extensions)))
        
                for param, value in summary_data:
                    table.add_row(param, value)
        
                console.print(table)
        
            def run(self):
                logger.debug("Main started.")
                args = self.arg_handler.get_arguments()
                logger.debug(f"Arguments: {args}")
        
                # Get user inputs using the new pattern
                args = self.arg_handler.get_user_inputs(args)
                logger.debug(f"Post-input arguments: {args}")
        
                if not args.input_path or not args.output_path:
                    Console().print("[bold red]Error: Input and output directories required.[/bold red]")
                    logger.error("Input and output directories required.")
                    return
        
                input_path = Path(args.input_path)
                output_path = Path(args.output_path)
                output_filename = self.ensure_md_extension(args.output_filename) if args.output_filename else "py__MarkdownGenerator.md"
                full_output = output_path / output_filename
        
                logger.debug(f"Input: {input_path}, Output: {output_path}, Filename: {output_filename}")
        
                self.ensure_directory_exists(output_path)
                self.display_summary(Console(), args)
        
                try:
                    generator = MarkdownGenerator(
                        root_dir=input_path,
                        output_file=full_output,
                        max_depth=args.depth,
                        extensions=args.extensions,
                        include_empty_dirs=args.include_empty_dirs,
                        exclude_dir_patterns=args.exclude_dir_patterns,
                        exclude_file_patterns=args.exclude_file_patterns,
                        include_all_files=args.include_all_files,
                        show_all_files_in_filestructure=args.show_all_files_in_filestructure,
                        use_gitignore=args.use_gitignore,
                        files_first=args.files_first,
                    )
                    generator.generate_markdown()
                    Console().print(f"\nMarkdown generated at [bold cyan]{full_output}[/bold cyan]\n")
        
                    # Cleanup log file if specified and successful execution
                    if args.cleanup_logs:
                        self.cleanup_logs()
        
                except Exception as e:
                    logger.error(f"Generation failed: {e}")
                    Console().print(f"\n[bold red]Error:[/bold red] {e}\n")
                    raise
        
            def cleanup_logs(self):
                """Clean up log files after successful execution"""
                logger.remove()
                log_file = Path("app.log.yml")
                if log_file.exists():
                    try:
                        log_file.unlink()
                        Console().print(f"Log file [bold green]{log_file}[/bold green] has been cleaned up.\n")
                    except Exception as e:
                        Console().print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")
                else:
                    Console().print(f"No log file found to clean up at {log_file}\n")
        
        def wait_for_user_exit():
            """Wait for user to press any key before exiting"""
            console = Console()
            console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
            try:
                input()
            except KeyboardInterrupt:
                pass
        
        def main():
            try:
                app = MarkdownGeneratorApp()
                app.run()
            except Exception as e:
                console = Console()
                console.print(f"\n[bold red]Error:[/bold red] {e}")
                logger.exception(f"Main execution error: {e}")
            finally:
                console = Console()
                console.print("\n[bold green]Finished processing.[/bold green]")
                wait_for_user_exit()
        
        if __name__ == "__main__":
            main()
    ```
    
```

---

#### `py__MarkdownGenerator.sublime-project`

```sublime-project
    {
        "folders": [
            {
                "path": ".",
                "folder_exclude_patterns": [
                    "__pycache__",
                    // "logs",
                    "env",
                    "venv",
                    ".git",
                    ".svn",
                    ".hg",
                    ".idea",
                    ".vscode",
                    "node_modules",
                    "*.egg-info",
                    "dist",
                    "build",
                    ".DS_Store"
                ],
                "file_exclude_patterns": [
                    "*.pyc",
                    "*.pyo",
                    // "*.log",
                    "*.tmp",
                    "*.sublime-workspace",
                    ".DS_Store",
                    "*.swp"
                ]
            }
        ],
        "settings": {
            "tab_size": 4,
            "default_line_ending": "unix",
            "translate_tabs_to_spaces": true,
            "ensure_newline_at_eof_on_save": true,
            "trim_trailing_white_space_on_save": true,
            "python_interpreter": "$project_path/venv/Scripts/python",
            "python_formatter": "black",
            "python_linter": "flake8",
            "python_format_on_save": false
        },
        "build_systems": [
            {
                "name": "py__MarkdownGenerator.sublime-project",
                "cmd": [
                    "$project_path/venv/Scripts/python",
                    "-u",
                    "${file}"
                ],
                "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
                "selector": "source.python",
                "shell": true,
                "working_dir": "${project_path}"
            }
        ]
    }
```

---

#### `pyproject.toml`

```toml
    [project]
    name = "markdown-generator"
    version = "1.0.0"
    description = "Generate comprehensive Markdown documentation from file structures with rich CLI interface"
    requires-python = ">=3.9"
    
    dependencies = [
        "chardet",
        "gitignore-parser",
        "loguru",
        "pyperclip",
        "PyYAML",
        "rich",
    ]
```

---

#### `run.bat`

```batch
    @ECHO OFF
    SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
    :: =============================================================================
    :: Markdown Generator - Universal Runner
    :: =============================================================================
    IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
    
    :: Check for uv installation
    WHERE uv >nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO [ERROR] uv is not installed or not in PATH
        ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
        ECHO.
        ECHO Falling back to traditional Python execution...
        python src\main.py --prompt
        GOTO :End
    )
    
    :: Check for pyproject.toml
    IF NOT EXIST "pyproject.toml" (
        ECHO [ERROR] pyproject.toml not found
        ECHO Please ensure the project is properly configured
        PAUSE>NUL & EXIT /B
    )
    
    :: Initialize environment if needed
    IF NOT EXIST ".venv" (
        ECHO [INFO] Initializing uv environment...
        uv sync
        IF ERRORLEVEL 1 (
            ECHO [ERROR] Failed to initialize environment
            PAUSE>NUL & EXIT /B
        )
        ECHO [SUCCESS] Environment initialized
        ECHO.
    )
    
    :: Run the application
    ECHO [INFO] Starting Markdown Generator...
    uv run python src\main.py --prompt
    
    :End
    ECHO.
    ECHO Press any key to restart or Ctrl+C to exit...
    PAUSE >NUL
    CLS
    GOTO :EOF
```

---

#### `src\main.old.py`

```python
    import argparse
    import fnmatch
    import logging
    import os
    import platform
    import re
    import socket
    import sys
    from enum import Enum
    from pathlib import Path
    
    import chardet
    import yaml
    from gitignore_parser import parse_gitignore
    from loguru import logger
    from rich import box
    from rich.console import Console
    from rich.prompt import Confirm, Prompt
    from rich.progress import (
        Progress,
        SpinnerColumn,
        TimeElapsedColumn,
        TextColumn,
        BarColumn,
        MofNCompleteColumn,
    )
    from rich.table import Table
    from rich.theme import Theme
    
    class Config:
    
        # Configuration Constants
        # =======================================================
        DEFAULT_DEPTH = 99
        DEFAULT_EXTENSIONS = ["py"]
        DEFAULT_INCLUDE_ALL_FILES = True
        DEFAULT_PREDEFINED_EXTENSION_GROUPS = False
        DEFAULT_FULL_STRUCTURE_PREVIEW = False
        DEFAULT_CLEANUP_LOGS = True
        DEFAULT_FILES_FIRST = True
    
        # Directory exclusions - always exclude completely
        EXCLUDED_DIRS = [
            ".backups", ".github", ".git", ".gpt", ".ignore",
            ".old", ".cmd", ".tmp", ".venv", ".versions", ".specstory",
            "__meta__", "__pycache__", "__tmp__", "venv", "node_modules",
        ]
    
        # Complete exclusions (excluded from both structure and content)
        EXCLUDED_PATTERNS = [
            # Compiled/bytecode files
            "*.pyc", "*.pyo", "*.class",
            # Database files
            "*.db",
            # Binary/executable files
            "*.exe", "*.dll", "*.so", "*.dylib", "*.bin",
            # Log files
            "*.log", "*.log.yml",
            # Special files
            ".new_hashes.py", ".original_hashes.py",
            # Commented out but kept for reference
            # "*.bat"
        ]
    
        # Regex patterns for complete exclusion
        EXCLUDED_REGEX = [r".*\.tmp$"]
    
        # Content-only exclusions (show in structure but exclude from content)
        CONTENT_ONLY_EXCLUDED_PATTERNS = [
            # Image files
            "*.ico", "*.png", "*.jpg", "*.jpeg", "*.heic", "*.gif", "*.bmp", "*.svg",
            # Audio files
            "*.mp3", "*.wav", "*.ogg", "*.flac", "*.aac",
            # Video files
            "*.mp4", "*.avi", "*.mov", "*.mkv", "*.webm",
            # Document files
            "*.pdf", "*.docx", "*.xlsx", "*.pptx",
            # Font files
            "*.ttf", "*.otf", "*.woff", "*.woff2",
            # Archive files
            "*.zip", "*.tar", "*.gz", "*.rar", "*.7z",
            # IDE files
            "*.sublime-workspace"
        ]
    
        # Regex patterns for content-only exclusion
        CONTENT_ONLY_EXCLUDED_REGEX = []
    
        # Default markers for visual indicators
        CONTENT_EXCLUDED_MARKER = "[-]"  # Marker for files in structure but excluded from content
    
        EXTENSION_GROUPS = {
            # "SublimeText": ["py", "*sublime*", "tmLanguage", "tmPreferences", "tmTheme", "stTheme"],
            "SublimeText": ["py", "sublime-commands", "sublime-keymap", "sublime-settings", "sublime-menu"],
            "Python": ["*pdm*", "env", "py", "pyi", "pyo", "toml", "jinja*"],
            "bat|py|txt": ["bat", "py", "txt"],
            "Web:React": ["ts", "tsx", "js", "json", "cjs", "css", "html", ],
            # "Web": ["css", "html", "js"],
            # "Data": ["cfg", "csv", "json", "xml"],
        }
    
        CODE_BLOCK_TYPES = {
            "py": "python", "json": "json", "nss": "java", "log": "text", "txt": "text",
            "md": "markdown", "html": "html", "htm": "html", "css": "css", "js": "javascript",
            "ts": "typescript", "xml": "xml", "yaml": "yaml", "yml": "yaml", "sh": "bash",
            "bat": "batch", "ini": "ini", "cfg": "ini", "java": "java", "c": "c", "cpp": "cpp",
            "h": "cpp", "hpp": "cpp", "cs": "csharp", "go": "go", "rb": "ruby", "php": "php",
            "sql": "sql", "swift": "swift", "kt": "kotlin", "rs": "rust", "r": "r", "pl": "perl",
            "lua": "lua", "scala": "scala", "vb": "vbnet",
        }
    
    class LoggerSetup:
    
        # Logging
        # =======================================================
        @staticmethod
        def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
            def yaml_sink(message):
                record = message.record
    
                time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"
                level_str = f"!{record['level'].name}"
                name_str = record['name']
                funcName_str = f"*{record['function']}"
                lineno_int = record["line"]
                msg = record["message"]
    
                if "\n" in msg:
                    lines = msg.split("\n")
                    message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
                else:
                    # Quote message if it has special characters
                    if ":" in msg:
                        message_str = f"'{msg}'"
                    else:
                        message_str = msg
    
                yaml_lines = [
                    f"- time: {time_str}",
                    f"  level: {level_str}",
                    f"  name: {name_str}",
                    f"  funcName: {funcName_str}",
                    f"  lineno: {lineno_int}",
                    f"  message: {message_str}",
                    ""
                ]
    
                with open(log_file, "a", encoding="utf-8") as f:
                    f.write("\n".join(yaml_lines) + "\n")
    
            logger.remove()
            logger.add(yaml_sink, level=level, enqueue=True)
    
        @staticmethod
        def initialize_logging():
            LoggerSetup.setup_yaml_logging()
    
    class ArgumentHandler:
    
        # Argument Parsing and Prompting
        # =======================================================
        def __init__(self):
            self.parser = self.parse_arguments()
    
        @staticmethod
        def parse_arguments():
            logger.debug("Setting up argument parser.")
            parser = argparse.ArgumentParser(description="Generate Markdown documentation from a file structure.")
            parser.add_argument('-i', '--input_path', type=str, help="Input directory path")
            parser.add_argument('-op', '--output_path', type=str, help="Output directory path")
            parser.add_argument('-of', '--output_filename', type=str, help="Output markdown filename")
            parser.add_argument('-d', '--depth', type=int, help="Max directory depth", default=Config.DEFAULT_DEPTH)
            parser.add_argument('--include_all_files', action='store_true', help="Include all file types", default=Config.DEFAULT_INCLUDE_ALL_FILES)
            parser.add_argument('-e', '--extensions', nargs='+', help="File extensions/groups", default=Config.DEFAULT_EXTENSIONS)
            parser.add_argument('--use-gitignore', action='store_true', help="Use .gitignore for exclusions", default=False)
            parser.add_argument('-edp', '--exclude_dir_patterns', nargs='+', help="Excluded directory patterns")
            parser.add_argument('-efp', '--exclude_file_patterns', nargs='+', help="Excluded file patterns")
            parser.add_argument('--show_all_files_in_filestructure', action='store_true', default=Config.DEFAULT_FULL_STRUCTURE_PREVIEW, help="Show all files in structure")
            parser.add_argument('--include_empty_dirs', action='store_true', help="Include empty directories")
            parser.add_argument('--files_first', action='store_true', default=Config.DEFAULT_FILES_FIRST, help="List files before directories in the output")
            parser.add_argument('--prompt', action='store_true', help="Prompt for input values")
            parser.add_argument('--log_to_current_dir', action='store_true', help="Log to current dir")
    
            # Arguments for Log Cleanup
            cleanup_logs_group = parser.add_mutually_exclusive_group()
            cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true', help="Clean up log files after successful execution")
            cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false', help="Do not clean up log files after successful execution")
            parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)
    
            logger.debug("Argument parser setup complete.")
            return parser
    
        def get_arguments(self):
            return self.parser.parse_args()
    
        def prompt_for_missing_arguments(self, args):
            logger.debug("Prompting for missing arguments.")
            console = Console()
    
            def print_section(title):
                console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)
    
            if args.prompt:
                # Default Settings prompt remains at the top.
                print_section("Default Settings")
                use_defaults = Confirm.ask("Use default settings?", default=True)
                logger.debug(f"Use defaults: {use_defaults}")
    
                if not use_defaults:
                    # --- File Types ---
                    print_section("File Types")
                    args.include_all_files = Confirm.ask("Include all file types?", default=False)
                    if not args.include_all_files:
                        if Confirm.ask("Use predefined extension group?", default=Config.DEFAULT_PREDEFINED_EXTENSION_GROUPS):
                            group_names = list(Config.EXTENSION_GROUPS.keys())
                            table = Table(header_style="bold magenta", box=box.SIMPLE)
                            table.add_column("No.", style="bold cyan", justify="right")
                            table.add_column("Group Name", style="bold cyan")
                            table.add_column("Extensions", style="magenta")
                            for i, group in enumerate(group_names, 1):
                                exts = ', '.join(Config.EXTENSION_GROUPS[group])
                                table.add_row(str(i), group, exts)
                            console.print(table)
                            choice = Prompt.ask("Choose group by number:", choices=[str(i) for i in range(1, len(group_names) + 1)])
                            selected_group = group_names[int(choice) - 1]
                            args.extensions = Config.EXTENSION_GROUPS[selected_group]
                        else:
                            exts_input = Prompt.ask("File extensions (space-separated):", default=' '.join(args.extensions or Config.DEFAULT_EXTENSIONS))
                            args.extensions = exts_input.split()
    
                    # --- Optional Settings ---
                    print_section("Optional Settings")
                    depth_input = Prompt.ask("Max depth:", default=str(args.depth))
                    args.depth = int(depth_input) if depth_input.isdigit() else Config.DEFAULT_DEPTH
                    if not args.include_all_files:
                        args.show_all_files_in_filestructure = Confirm.ask("Include unmatched files?", default=args.show_all_files_in_filestructure)
                    args.include_empty_dirs = Confirm.ask("Include empty dirs?", default=args.include_empty_dirs)
                    args.files_first = Confirm.ask("List files before directories?", default=args.files_first)
    
                    # --- Exclusions ---
                    print_section("Exclusions")
                    args.use_gitignore = Confirm.ask("Use .gitignore?", default=args.use_gitignore)
                    excl_dir = Prompt.ask("Exclude folders:", default=' '.join(args.exclude_dir_patterns or []))
                    args.exclude_dir_patterns = excl_dir.split() if excl_dir else []
                    excl_file = Prompt.ask("Exclude files:", default=' '.join(args.exclude_file_patterns or []))
                    args.exclude_file_patterns = excl_file.split() if excl_file else []
    
                    # --- Input/Output ---
                    print_section("Input/Output")
                    args.input_path = Prompt.ask("Input folder:", default=args.input_path)
                    args.output_path = Prompt.ask("Output folder:", default=args.output_path)
                    args.output_filename = Prompt.ask("Output file (.md):", default=args.output_filename or 'py__MarkdownGenerator.md')
    
                    # --- Cleanup Logs ---
                    args.cleanup_logs = Confirm.ask("Clean up log files after successful execution?", default=args.cleanup_logs)
                else:
                    # Assign defaults if using default settings
                    args.input_path = args.input_path or ""
                    args.output_path = args.output_path or ""
                    args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
                    args.depth = args.depth or Config.DEFAULT_DEPTH
                    args.include_all_files = args.include_all_files or False
                    args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
                    args.use_gitignore = args.use_gitignore or False
                    args.exclude_dir_patterns = args.exclude_dir_patterns or []
                    args.exclude_file_patterns = args.exclude_file_patterns or []
                    args.show_all_files_in_filestructure = args.show_all_files_in_filestructure or Config.DEFAULT_FULL_STRUCTURE_PREVIEW
                    args.include_empty_dirs = args.include_empty_dirs or False
                    args.files_first = args.files_first or Config.DEFAULT_FILES_FIRST
                    args.cleanup_logs = args.cleanup_logs
    
                    logger.debug(f"input_path: {args.input_path}")
                    logger.debug(f"output_path: {args.output_path}")
                    logger.debug(f"output_filename: {args.output_filename}")
                    logger.debug(f"depth: {args.depth}")
                    logger.debug(f"include_all_files: {args.include_all_files}")
                    logger.debug(f"extensions: {args.extensions}")
                    logger.debug(f"use_gitignore: {args.use_gitignore}")
                    logger.debug(f"exclude_dir_patterns: {args.exclude_dir_patterns}")
                    logger.debug(f"exclude_file_patterns: {args.exclude_file_patterns}")
                    logger.debug(f"show_all_files_in_filestructure: {args.show_all_files_in_filestructure}")
                    logger.debug(f"include_empty_dirs: {args.include_empty_dirs}")
                    logger.debug(f"files_first: {args.files_first}")
                    logger.debug(f"cleanup_logs: {args.cleanup_logs}")
    
            logger.debug("Argument prompting complete.")
            return args
    
    class FileExcluder:
    
        # File Exclusion Logic
        # =======================================================
        def __init__(self, root_dir: Path, exclude_dir_patterns=None, exclude_file_patterns=None,
                     exclude_regex=None, use_gitignore=False):
            self.root_dir = root_dir
            self.exclude_dir_patterns = exclude_dir_patterns or []
            self.exclude_file_patterns = exclude_file_patterns or []
            self.exclude_regex = exclude_regex or []
            self.gitignore_patterns = self.load_gitignore_patterns() if use_gitignore else []
            self.exclusion_counters = {"dirs": 0, "patterns": 0, "regex": 0, "gitignore": 0, "content_only": 0}
            # Track files that should be excluded from content but shown in structure
            self.content_only_excluded_files = set()
    
        def load_gitignore_patterns(self):
            gitignore_path = self.root_dir / '.gitignore'
            if not gitignore_path.exists():
                logger.debug(f"No .gitignore found at {gitignore_path}")
                return []
    
            with open(gitignore_path, 'r') as file:
                lines = file.readlines()
    
            patterns = []
            for line in lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('#'):
                    if stripped.endswith('/'):
                        stripped = stripped.rstrip('/') + '/**'
                    elif stripped.startswith('/'):
                        stripped = stripped.lstrip('/')
                    patterns.append(stripped)
            logger.debug(f".gitignore patterns: {patterns}")
            return patterns
    
        def is_content_excluded(self, path: Path) -> bool:
            """
            Check if a file should be excluded from content but shown in structure.
            Returns True if the file should be excluded from content only.
            """
            if path.is_dir():
                return False  # Directories are never content-excluded
    
            relative_filepath = path.relative_to(self.root_dir).as_posix()
    
            # Check if file matches any content-only exclusion patterns
            for pattern in Config.CONTENT_ONLY_EXCLUDED_PATTERNS:
                if fnmatch.fnmatch(path.name, pattern):
                    self.exclusion_counters['content_only'] += 1
                    self.content_only_excluded_files.add(path)
                    return True
    
            # Check content-only excluded regex patterns
            for pattern in Config.CONTENT_ONLY_EXCLUDED_REGEX:
                if re.match(pattern, relative_filepath):
                    self.exclusion_counters['content_only'] += 1
                    self.content_only_excluded_files.add(path)
                    return True
    
            return False
    
        def is_excluded(self, path: Path) -> bool:
            """
            Check if a path should be completely excluded from both structure and content.
            Returns True if the path should be completely excluded.
            """
            relative_filepath = path.relative_to(self.root_dir).as_posix()
    
            # Check if any part of the path is in EXCLUDED_DIRS
            for part in path.parts:
                if part in Config.EXCLUDED_DIRS + self.exclude_dir_patterns:
                    if path.is_dir():
                        self.exclusion_counters['dirs'] += 1
                    else:
                        self.exclusion_counters['patterns'] += 1
                    return True
    
            # Check excluded regex patterns
            for pattern in Config.EXCLUDED_REGEX + self.exclude_regex:
                if re.match(pattern, relative_filepath):
                    self.exclusion_counters['regex'] += 1
                    return True
    
            # Check gitignore patterns
            for pattern in self.gitignore_patterns:
                if fnmatch.fnmatch(relative_filepath, pattern):
                    self.exclusion_counters['gitignore'] += 1
                    return True
    
            # Check excluded file patterns
            if path.is_file():
                for pattern in Config.EXCLUDED_PATTERNS + self.exclude_file_patterns:
                    if fnmatch.fnmatch(path.name, pattern):
                        self.exclusion_counters['patterns'] += 1
                        return True
    
            return False
    
    class MarkdownGenerator:
    
        # Markdown Generation Logic
        # =======================================================
        def __init__(self, root_dir: Path, output_file: Path, max_depth=None, extensions=None,
                     include_empty_dirs=False, exclude_dir_patterns=None, exclude_file_patterns=None,
                     include_all_files=False, show_all_files_in_filestructure=False, use_gitignore=False,
                     files_first=False):
            self.root_dir = root_dir
            self.output_file = output_file
            self.max_depth = max_depth
            self.extensions = self.resolve_extensions(extensions)
            self.include_empty_dirs = include_empty_dirs
            self.exclude_dir_patterns = exclude_dir_patterns
            self.exclude_file_patterns = exclude_file_patterns
            self.include_all_files = include_all_files
            self.show_all_files_in_filestructure = show_all_files_in_filestructure
            self.use_gitignore = use_gitignore
            self.files_first = files_first
            self.excluder = FileExcluder(
                root_dir=self.root_dir,
                exclude_dir_patterns=self.exclude_dir_patterns,
                exclude_file_patterns=self.exclude_file_patterns,
                use_gitignore=self.use_gitignore
            )
            self.console = Console()
    
        def resolve_extensions(self, extensions_list):
            logger.debug(f"Resolving extensions: {extensions_list}")
            resolved = []
            for item in extensions_list:
                if item in Config.EXTENSION_GROUPS:
                    resolved.extend(Config.EXTENSION_GROUPS[item])
                    logger.debug(f"Group '{item}' -> {Config.EXTENSION_GROUPS[item]}")
                else:
                    resolved.append(item)
                    logger.debug(f"Extension '{item}' added")
            logger.debug(f"Final extensions: {resolved}")
            return resolved
    
        def get_code_block_type(self, ext):
            return Config.CODE_BLOCK_TYPES.get(ext, ext)
    
        def generate_markdown_for_file(self, file_path: Path):
            relative_filepath = file_path.relative_to(self.root_dir)
            ext = file_path.suffix[1:]
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'utf-16', 'utf-16-le', 'utf-16-be']
            content = None
    
            try:
                with open(file_path, 'rb') as f:
                    raw = f.read(10000)
                detected = chardet.detect(raw)['encoding']
                if detected:
                    encodings.insert(0, detected)
            except Exception as e:
                logger.error(f"Encoding detection error for {file_path}: {e}")
    
            for enc in encodings:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.error(f"Error reading {file_path} with {enc}: {e}")
    
            if content is None:
                return f"#### `{relative_filepath}`\n\nError: Unable to read file.\n\n"
    
            # Current file header
            file_header = f"\n\n---\n\n#### `{relative_filepath}`\n\n"
    
            # Determine block type based on file extension
            block_type = self.get_code_block_type(ext)
            block_prefix = f"```{block_type}\n" if block_type else ""
            block_suffix = "\n```" if block_type else ""
    
            # Indent content by 4 spaces and combine elements
            lines = content.splitlines()
            indented_content = '\n'.join(f"    {line}" for line in lines)
            content_entry = f"{file_header}{block_prefix}{indented_content}{block_suffix}\n\n"
    
            return content_entry
    
        def build_tree(self, paths):
            tree = {}
            for p in paths:
                parts = p.relative_to(self.root_dir).parts
                node = tree
                for part in parts[:-1]:
                    node = node.setdefault(part, {})
                node.setdefault(parts[-1], {})
            return tree
    
        def is_content_excluded_path(self, path):
            """Check if a path is in the content-excluded files set"""
            return path in self.excluder.content_only_excluded_files
    
        def print_tree(self, node, prefix="", current_path=None):
            lines = []
            keys = list(node.keys())
    
            if current_path is None:
                current_path = self.root_dir
    
            # Separate files and directories
            files = [k for k in keys if not node[k]]  # Empty dict means it's a file
            dirs = [k for k in keys if node[k]]       # Non-empty dict means it's a directory
    
            # Sort files and directories separately
            files.sort()
            dirs.sort()
    
            # Combine files and directories based on files_first parameter
            sorted_keys = files + dirs if self.files_first else dirs + files
    
            for i, key in enumerate(sorted_keys):
                connector = "└──" if i == len(sorted_keys)-1 else "├──"
    
                # For files, check if content-excluded
                if key in files:
                    path = current_path / key
                    if self.excluder.is_content_excluded(path):
                        lines.append(f"{prefix}{connector} {key} {Config.CONTENT_EXCLUDED_MARKER}")
                    else:
                        lines.append(f"{prefix}{connector} {key}")
                else:
                    # For directories, no exclusion marker
                    lines.append(f"{prefix}{connector} {key}")
    
                sub_node = node[key]
                if sub_node:  # If it's a directory
                    extension = "    " if i == len(sorted_keys)-1 else "│   "
                    next_path = current_path / key
                    lines.extend(self.print_tree(sub_node, prefix=prefix+extension, current_path=next_path))
            return lines
    
        def generate_markdown(self):
            try:
                gitignore_patterns = self.excluder.gitignore_patterns
                if gitignore_patterns:
                    self.exclude_dir_patterns = (self.exclude_dir_patterns or []) + gitignore_patterns
                    self.exclude_file_patterns = (self.exclude_file_patterns or []) + gitignore_patterns
    
                logger.debug(f"Generating markdown for {self.root_dir} -> {self.output_file}")
                logger.debug(f"Extensions: {self.extensions}, Include all: {self.include_all_files}, Show all in structure: {self.show_all_files_in_filestructure}")
    
                structure_patterns = ["*"] if self.include_all_files or self.show_all_files_in_filestructure else [f"*.{ext}" for ext in self.extensions]
                content_patterns = ["*"] if self.include_all_files else [f"*.{ext}" for ext in self.extensions]
    
                markdown_content = f"# Dir `{self.root_dir.stem}`\n\n"
                # Always show the marker explanation if content-excluded files can appear in the structure
                markdown_content += f"*Files marked `{Config.CONTENT_EXCLUDED_MARKER}` are shown in structure but not included in content.*\n\n"
    
                excluded = []
                processed = []
                counters = self.excluder.exclusion_counters
    
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    MofNCompleteColumn(),
                    TimeElapsedColumn(),
                    console=self.console,
                ) as progress:
                    paths = sorted(self.root_dir.rglob("*"))
                    total = len(paths)
                    logger.debug(f"Found {total} paths.")
                    task1 = progress.add_task("[cyan]Gathering file structure...", total=total)
    
                    included_paths = []
                    for path in paths:
                        if self.excluder.is_excluded(path):
                            excluded.append(path)
                        else:
                            if ((self.max_depth is None or len(path.relative_to(self.root_dir).parts) <= self.max_depth)
                                and (path.is_dir() or any(fnmatch.fnmatch(path.name, pat) for pat in structure_patterns))
                                and (self.include_empty_dirs or not path.is_dir() or
                                     any(
                                         not self.excluder.is_excluded(f) and f.is_file() and any(fnmatch.fnmatch(f.name, p) for p in structure_patterns)
                                         for f in path.rglob("*")
                                     ))
                               ):
                                included_paths.append(path)
                        progress.update(task1, advance=1)
    
                    tree = self.build_tree(included_paths)
                    file_structure_lines = self.print_tree(tree)
                    file_structure_str = "### File Structure\n\n```\n" + "\n".join(file_structure_lines) + "\n```\n"
                    markdown_content += file_structure_str
    
                    progress.update(task1, completed=total)
                    logger.debug(f"Excluded {len(excluded)} paths: {counters}")
    
                    # Files to process for content (excluding content-only excluded files)
                    files = [p for p in paths if p not in excluded and p.is_file()
                             and not self.excluder.is_content_excluded(p)
                             and (self.max_depth is None or len(p.relative_to(self.root_dir).parts) <= self.max_depth)
                             and any(fnmatch.fnmatch(p.name, pat) for pat in content_patterns)]
    
                    # Sort files based on files_first parameter
                    def sort_key(path):
                        parts = path.relative_to(self.root_dir).parts
                        if self.files_first:
                            # For files-first, we want files in the current directory to come before subdirectories
                            return (len(parts), 0 if len(parts) == 1 else 1) + parts
                        else:
                            # For directories-first, we want files in subdirectories to be grouped with their directories
                            return parts
    
                    files.sort(key=sort_key)
                    logger.debug(f"{len(files)} files to process.")
                    task2 = progress.add_task("[cyan]Processing files for content...", total=len(files))
    
                    for idx, file in enumerate(files, 1):
                        try:
                            content = self.generate_markdown_for_file(file)
                            markdown_content += f"{content}"
                            processed.append(file)
                        except Exception as e:
                            logger.error(f"Failed on {file}: {e}")
                        finally:
                            progress.update(task2, advance=1)
    
                    logger.debug(f"Processed {len(processed)} files.")
    
                # Removes consecutive blank lines (preserving at most one blank line between text lines).
                markdown_content_clean = re.sub(r"\n{2,}", "\n\n", markdown_content)
                self.output_file.write_text(markdown_content_clean, encoding="utf-8")
                logger.info(f"Markdown generated at {self.output_file}")
    
            except Exception as e:
                logger.error(f"Markdown generation failed: {e}")
                raise
    
    class MarkdownGeneratorApp:
    
        # Main Application Logic
        # =======================================================
        def __init__(self):
            LoggerSetup.initialize_logging()
            self.arg_handler = ArgumentHandler()
    
        @staticmethod
        def ensure_md_extension(filename):
            return f"{filename}.md" if not filename.endswith('.md') else filename
    
        @staticmethod
        def ensure_directory_exists(directory: Path):
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created output directory: {directory}")
    
        @staticmethod
        def clear_console():
            os.system("cls" if platform.system() == "Windows" else "clear")
    
        @staticmethod
        def display_summary(console, args):
            table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
            table.add_column("Parameter", style="dim", width=30)
            table.add_column("Value", style="bold cyan")
    
            summary_data = [
                ("Input directory path", str(args.input_path)),
                ("Output markdown file path", str(args.output_path)),
                ("Output filename", str(args.output_filename)),
                ("Maximum directory depth", str(args.depth)),
                ("Include all files", "Yes" if args.include_all_files else "No"),
                ("Show all files in filestructure", "Yes" if args.show_all_files_in_filestructure else "No"),
                ("Include empty directories", "Yes" if args.include_empty_dirs else "No"),
                ("Files before directories", "Yes" if args.files_first else "No"),
                ("Excluded directory patterns", ', '.join(args.exclude_dir_patterns) if args.exclude_dir_patterns else "None"),
                ("Excluded file patterns", ', '.join(args.exclude_file_patterns) if args.exclude_file_patterns else "None"),
                ("Use .gitignore for exclusions", "Yes" if args.use_gitignore else "No"),
                ("Cleanup log files after success", "Yes" if args.cleanup_logs else "No"),
            ]
    
            if not args.include_all_files:
                summary_data.insert(6, ("File extensions", ', '.join(args.extensions)))
    
            for param, value in summary_data:
                table.add_row(param, value)
    
            console.print(table)
    
        def run(self):
            logger.debug("Main started.")
            parser = self.arg_handler.parser
    
            while True:
                self.clear_console()
                logger.debug("Console cleared.")
                args = self.arg_handler.get_arguments()
                logger.debug(f"Arguments: {args}")
                args = self.arg_handler.prompt_for_missing_arguments(args)
                logger.debug(f"Post-prompt arguments: {args}")
    
                if not args.input_path or not args.output_path:
                    Console().print("[bold red]Error: Input and output directories required.[/bold red]")
                    logger.error("Input and output directories required.")
                    continue
    
                input_path = Path(args.input_path)
                output_path = Path(args.output_path)
                output_filename = self.ensure_md_extension(args.output_filename) if args.output_filename else "py__MarkdownGenerator.md"
                full_output = output_path / output_filename
    
                logger.debug(f"Input: {input_path}, Output: {output_path}, Filename: {output_filename}")
    
                self.ensure_directory_exists(output_path)
                self.display_summary(Console(), args)
    
                if Confirm.ask("Proceed?", default=True):
                    logger.debug("User confirmed.")
                    success = False
                    try:
                        generator = MarkdownGenerator(
                            root_dir=input_path,
                            output_file=full_output,
                            max_depth=args.depth,
                            extensions=args.extensions,
                            include_empty_dirs=args.include_empty_dirs,
                            exclude_dir_patterns=args.exclude_dir_patterns,
                            exclude_file_patterns=args.exclude_file_patterns,
                            include_all_files=args.include_all_files,
                            show_all_files_in_filestructure=args.show_all_files_in_filestructure,
                            use_gitignore=args.use_gitignore,
                            files_first=args.files_first,
                        )
                        generator.generate_markdown()
                        Console().print(f"\nMarkdown generated at [bold cyan]{full_output}[/bold cyan]\n")
                        success = True
                    except Exception as e:
                        logger.error(f"Generation failed: {e}")
                        Console().print(f"\n[bold red]Error:[/bold red] {e}\n")
                        action = Prompt.ask("Proceed?", choices=["Retry", "Skip", "Cancel"], default="Retry")
                        if action == "Retry":
                            continue
                        elif action == "Skip":
                            logger.info("User skipped.")
                            break
                        else:
                            Console().print("[bold red]Cancelled by user.[/bold red]")
                            logger.info("Cancelled by user.")
                            sys.exit(0)
    
                    # Cleanup log file if specified and successful execution
                    if success and args.cleanup_logs:
                        logger.remove()
                        log_file = Path("app.log.yml")
                        if log_file.exists():
                            try:
                                log_file.unlink()
                                Console().print(f"Log file [bold green]{log_file}[/bold green] has been cleaned up.\n")
                            except Exception as e:
                                Console().print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")
                        else:
                            Console().print(f"No log file found to clean up at {log_file}\n")
    
                    break
                else:
                    Console().print("Cancelled. Restarting...", style="bold red")
                    logger.info("Cancelled by user. Restarting...")
    
    def main():
        app = MarkdownGeneratorApp()
        app.run()
    
    if __name__ == "__main__":
        main()
```

---

#### `src\main.py`

```python
    import argparse
    import fnmatch
    import os
    import platform
    import re
    import sys
    from pathlib import Path
    
    import chardet
    import pyperclip
    import yaml
    from gitignore_parser import parse_gitignore
    from loguru import logger
    from rich import box
    from rich.console import Console
    from rich.prompt import Confirm, Prompt
    from rich.progress import (
        Progress,
        SpinnerColumn,
        TimeElapsedColumn,
        TextColumn,
        BarColumn,
        MofNCompleteColumn,
    )
    from rich.table import Table
    
    class Config:
    
        # Configuration Constants
        # =======================================================
        DEFAULT_DEPTH = 99
        DEFAULT_EXTENSIONS = ["py"]
        DEFAULT_INCLUDE_ALL_FILES = True
        DEFAULT_PREDEFINED_EXTENSION_GROUPS = False
        DEFAULT_FULL_STRUCTURE_PREVIEW = False
        DEFAULT_CLEANUP_LOGS = True
        DEFAULT_FILES_FIRST = True
    
        # Directory exclusions - always exclude completely
        EXCLUDED_DIRS = [
            ".backups", ".github", ".git", ".gpt", ".ignore",
            ".old", ".cmd", ".tmp", ".venv", ".versions", ".specstory",
            "__meta__", "__pycache__", "__tmp__", "venv", "node_modules",
        ]
    
        # Complete exclusions (excluded from both structure and content)
        EXCLUDED_PATTERNS = [
            # Compiled/bytecode files
            "*.pyc", "*.pyo", "*.class", 
            # Database files
            "*.db", 
            # Binary/executable files
            "*.exe", "*.dll", "*.so", "*.dylib", "*.bin",
            # Log files
            "*.log", "*.log.yml",  "uv.lock",
            # Special files
            ".new_hashes.py", ".original_hashes.py",
            # Commented out but kept for reference
            # "*.bat"
        ]
    
        # Regex patterns for complete exclusion
        EXCLUDED_REGEX = [r".*\.tmp$"]
    
        # Content-only exclusions (show in structure but exclude from content)
        CONTENT_ONLY_EXCLUDED_PATTERNS = [
            # Image files
            "*.ico", "*.png", "*.jpg", "*.jpeg", "*.heic", "*.gif", "*.bmp", "*.svg",
            # Audio files
            "*.mp3", "*.wav", "*.ogg", "*.flac", "*.aac",
            # Video files
            "*.mp4", "*.avi", "*.mov", "*.mkv", "*.webm", "*.m4a",
            # Document files
            "*.pdf", "*.docx", "*.xlsx", "*.pptx",
            # Font files
            "*.ttf", "*.otf", "*.woff", "*.woff2",
            # Archive files
            "*.zip", "*.tar", "*.gz", "*.rar", "*.7z",
            # IDE files
            "*.sublime-workspace"
        ]
        
        # Regex patterns for content-only exclusion
        CONTENT_ONLY_EXCLUDED_REGEX = []
    
        # Default markers for visual indicators
        CONTENT_EXCLUDED_MARKER = "[-]"  # Marker for files in structure but excluded from content
    
        EXTENSION_GROUPS = {
            # "SublimeText": ["py", "*sublime*", "tmLanguage", "tmPreferences", "tmTheme", "stTheme"],
            "SublimeText": ["py", "sublime-commands", "sublime-keymap", "sublime-settings", "sublime-menu"],
            "Python": ["*pdm*", "env", "py", "pyi", "pyo", "toml", "jinja*"],
            "bat|py|txt": ["bat", "py", "txt"],
            "Web:React": ["ts", "tsx", "js", "json", "cjs", "css", "html", ],
            # "Web": ["css", "html", "js"],
            # "Data": ["cfg", "csv", "json", "xml"],
        }
    
        CODE_BLOCK_TYPES = {
            "py": "python", "json": "json", "nss": "java", "log": "text", "txt": "text",
            "md": "markdown", "html": "html", "htm": "html", "css": "css", "js": "javascript",
            "ts": "typescript", "xml": "xml", "yaml": "yaml", "yml": "yaml", "sh": "bash",
            "bat": "batch", "ini": "ini", "cfg": "ini", "java": "java", "c": "c", "cpp": "cpp",
            "h": "cpp", "hpp": "cpp", "cs": "csharp", "go": "go", "rb": "ruby", "php": "php",
            "sql": "sql", "swift": "swift", "kt": "kotlin", "rs": "rust", "r": "r", "pl": "perl",
            "lua": "lua", "scala": "scala", "vb": "vbnet",
        }
    
    class LoggerSetup:
    
        # Logging
        # =======================================================
        @staticmethod
        def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
            def yaml_sink(message):
                record = message.record
    
                time_str = f"{record['time'].strftime('%Y-%m-%d %H:%M:%S')}"
                level_str = f"!{record['level'].name}"
                name_str = record['name']
                funcName_str = f"*{record['function']}"
                lineno_int = record["line"]
                msg = record["message"]
    
                if "\n" in msg:
                    lines = msg.split("\n")
                    message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
                else:
                    # Quote message if it has special characters
                    if ":" in msg:
                        message_str = f"'{msg}'"
                    else:
                        message_str = msg
    
                yaml_lines = [
                    f"- time: {time_str}",
                    f"  level: {level_str}",
                    f"  name: {name_str}",
                    f"  funcName: {funcName_str}",
                    f"  lineno: {lineno_int}",
                    f"  message: {message_str}",
                    ""
                ]
    
                with open(log_file, "a", encoding="utf-8") as f:
                    f.write("\n".join(yaml_lines) + "\n")
    
            logger.remove()
            logger.add(yaml_sink, level=level, enqueue=True)
    
        @staticmethod
        def initialize_logging():
            LoggerSetup.setup_yaml_logging()
    
    class ArgumentHandler:
    
        # Argument Parsing and Prompting
        # =======================================================
        def __init__(self):
            self.parser = self.parse_arguments()
    
        @staticmethod
        def parse_arguments():
            logger.debug("Setting up argument parser.")
            parser = argparse.ArgumentParser(description="Generate Markdown documentation from a file structure.")
            parser.add_argument('-i', '--input_path', type=str, help="Input directory path")
            parser.add_argument('-op', '--output_path', type=str, help="Output directory path")
            parser.add_argument('-of', '--output_filename', type=str, help="Output markdown filename")
            parser.add_argument('-d', '--depth', type=int, help="Max directory depth", default=Config.DEFAULT_DEPTH)
            parser.add_argument('--include_all_files', action='store_true', help="Include all file types", default=Config.DEFAULT_INCLUDE_ALL_FILES)
            parser.add_argument('-e', '--extensions', nargs='+', help="File extensions/groups", default=Config.DEFAULT_EXTENSIONS)
            parser.add_argument('--use-gitignore', action='store_true', help="Use .gitignore for exclusions", default=False)
            parser.add_argument('-edp', '--exclude_dir_patterns', nargs='+', help="Excluded directory patterns")
            parser.add_argument('-efp', '--exclude_file_patterns', nargs='+', help="Excluded file patterns")
            parser.add_argument('--show_all_files_in_filestructure', action='store_true', default=Config.DEFAULT_FULL_STRUCTURE_PREVIEW, help="Show all files in structure")
            parser.add_argument('--include_empty_dirs', action='store_true', help="Include empty directories")
            parser.add_argument('--files_first', action='store_true', default=Config.DEFAULT_FILES_FIRST, help="List files before directories in the output")
            parser.add_argument('-c', '--copy_to_clipboard', action='store_true', default=True, help="Copy generated markdown to clipboard")
            parser.add_argument('--prompt', action='store_true', help="Prompt for input values")
            parser.add_argument('--log_to_current_dir', action='store_true', help="Log to current dir")
    
            # Arguments for Log Cleanup
            cleanup_logs_group = parser.add_mutually_exclusive_group()
            cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true', help="Clean up log files after successful execution")
            cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false', help="Do not clean up log files after successful execution")
            parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)
    
            logger.debug("Argument parser setup complete.")
            return parser
    
        def get_arguments(self):
            return self.parser.parse_args()
    
        def get_user_inputs(self, args):
            """Get user inputs following the pattern from main.old.py"""
            logger.debug("Getting user inputs.")
            console = Console()
    
            def print_section(title):
                console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)
    
            # If --prompt is used, ask for all configuration options
            if args.prompt:
                # Default Settings prompt comes first, following main.old.py pattern
                print_section("Default Settings")
                use_defaults = Confirm.ask("Use default settings?", default=True)
                logger.debug(f"Use defaults: {use_defaults}")
    
                if not use_defaults:
                    # --- Post-Processing ---
                    print_section("Post-Processing")
                    args.copy_to_clipboard = Confirm.ask("Copy generated markdown to clipboard?", default=True)
    
                    # --- File Types ---
                    print_section("File Types")
                    args.include_all_files = Confirm.ask("Include all file types?", default=args.include_all_files)
    
                    if not args.include_all_files:
                        if Confirm.ask("Use predefined extension group?", default=Config.DEFAULT_PREDEFINED_EXTENSION_GROUPS):
                            group_names = list(Config.EXTENSION_GROUPS.keys())
                            table = Table(header_style="bold magenta", box=box.SIMPLE)
                            table.add_column("No.", style="bold cyan", justify="right")
                            table.add_column("Group Name", style="bold cyan")
                            table.add_column("Extensions", style="magenta")
                            for i, group in enumerate(group_names, 1):
                                exts = ', '.join(Config.EXTENSION_GROUPS[group])
                                table.add_row(str(i), group, exts)
                            console.print(table)
                            choice = Prompt.ask("Choose group by number:", choices=[str(i) for i in range(1, len(group_names) + 1)])
                            selected_group = group_names[int(choice) - 1]
                            args.extensions = Config.EXTENSION_GROUPS[selected_group]
                        else:
                            exts_input = Prompt.ask("File extensions (space-separated):", default=' '.join(args.extensions or Config.DEFAULT_EXTENSIONS))
                            args.extensions = exts_input.split()
    
                    # --- Optional Settings ---
                    print_section("Optional Settings")
                    depth_input = Prompt.ask("Max depth:", default=str(args.depth))
                    args.depth = int(depth_input) if depth_input.isdigit() else Config.DEFAULT_DEPTH
    
                    if not args.include_all_files:
                        args.show_all_files_in_filestructure = Confirm.ask("Include unmatched files?", default=args.show_all_files_in_filestructure)
    
                    args.include_empty_dirs = Confirm.ask("Include empty dirs?", default=args.include_empty_dirs)
                    args.files_first = Confirm.ask("List files before directories?", default=args.files_first)
    
                    # --- Exclusions ---
                    print_section("Exclusions")
                    args.use_gitignore = Confirm.ask("Use .gitignore?", default=args.use_gitignore)
                    excl_dir = Prompt.ask("Exclude folders:", default=' '.join(args.exclude_dir_patterns or []))
                    args.exclude_dir_patterns = excl_dir.split() if excl_dir else []
                    excl_file = Prompt.ask("Exclude files:", default=' '.join(args.exclude_file_patterns or []))
                    args.exclude_file_patterns = excl_file.split() if excl_file else []
    
                    # --- Input/Output ---
                    print_section("Input/Output")
                    if not args.input_path:
                        args.input_path = Prompt.ask("Input folder:", default=args.input_path)
                    if not args.output_path:
                        args.output_path = Prompt.ask("Output folder:", default=args.output_path)
                    if not args.output_filename:
                        args.output_filename = Prompt.ask("Output file (.md):", default=args.output_filename or 'py__MarkdownGenerator.md')
    
                    # --- Cleanup Logs ---
                    args.cleanup_logs = Confirm.ask("Clean up log files after successful execution?", default=args.cleanup_logs)
                else:
                    # Assign defaults if using default settings
                    args.input_path = args.input_path or ""
                    args.output_path = args.output_path or ""
                    args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
                    args.depth = args.depth or Config.DEFAULT_DEPTH
                    args.include_all_files = args.include_all_files or False
                    args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
                    args.use_gitignore = args.use_gitignore or False
                    args.exclude_dir_patterns = args.exclude_dir_patterns or []
                    args.exclude_file_patterns = args.exclude_file_patterns or []
                    args.show_all_files_in_filestructure = args.show_all_files_in_filestructure or Config.DEFAULT_FULL_STRUCTURE_PREVIEW
                    args.include_empty_dirs = args.include_empty_dirs or False
                    args.files_first = args.files_first or Config.DEFAULT_FILES_FIRST
                    args.cleanup_logs = args.cleanup_logs
                    args.copy_to_clipboard = getattr(args, 'copy_to_clipboard', True)
    
            # Ensure defaults for any unset values
            args.input_path = args.input_path or os.getcwd()
            args.output_path = args.output_path or os.getcwd()
            args.output_filename = args.output_filename or 'py__MarkdownGenerator.md'
            args.extensions = args.extensions or Config.DEFAULT_EXTENSIONS
            args.exclude_dir_patterns = args.exclude_dir_patterns or []
            args.exclude_file_patterns = args.exclude_file_patterns or []
    
            logger.debug("User input collection complete.")
            return args
    
    class FileExcluder:
    
        # File Exclusion Logic
        # =======================================================
        def __init__(self, root_dir: Path, exclude_dir_patterns=None, exclude_file_patterns=None,
                     exclude_regex=None, use_gitignore=False):
            self.root_dir = root_dir
            self.exclude_dir_patterns = exclude_dir_patterns or []
            self.exclude_file_patterns = exclude_file_patterns or []
            self.exclude_regex = exclude_regex or []
            self.gitignore_patterns = self.load_gitignore_patterns() if use_gitignore else []
            self.exclusion_counters = {"dirs": 0, "patterns": 0, "regex": 0, "gitignore": 0, "content_only": 0}
            # Track files that should be excluded from content but shown in structure
            self.content_only_excluded_files = set()
    
        def load_gitignore_patterns(self):
            gitignore_path = self.root_dir / '.gitignore'
            if not gitignore_path.exists():
                logger.debug(f"No .gitignore found at {gitignore_path}")
                return []
    
            with open(gitignore_path, 'r') as file:
                lines = file.readlines()
    
            patterns = []
            for line in lines:
                stripped = line.strip()
                if stripped and not stripped.startswith('#'):
                    if stripped.endswith('/'):
                        stripped = stripped.rstrip('/') + '/**'
                    elif stripped.startswith('/'):
                        stripped = stripped.lstrip('/')
                    patterns.append(stripped)
            logger.debug(f".gitignore patterns: {patterns}")
            return patterns
    
        def is_content_excluded(self, path: Path) -> bool:
            """
            Check if a file should be excluded from content but shown in structure.
            Returns True if the file should be excluded from content only.
            """
            if path.is_dir():
                return False  # Directories are never content-excluded
    
            relative_filepath = path.relative_to(self.root_dir).as_posix()
    
            # Check if file matches any content-only exclusion patterns
            for pattern in Config.CONTENT_ONLY_EXCLUDED_PATTERNS:
                if fnmatch.fnmatch(path.name, pattern):
                    self.exclusion_counters['content_only'] += 1
                    self.content_only_excluded_files.add(path)
                    return True
    
            # Check content-only excluded regex patterns
            for pattern in Config.CONTENT_ONLY_EXCLUDED_REGEX:
                if re.match(pattern, relative_filepath):
                    self.exclusion_counters['content_only'] += 1
                    self.content_only_excluded_files.add(path)
                    return True
    
            return False
    
        def is_excluded(self, path: Path) -> bool:
            """
            Check if a path should be completely excluded from both structure and content.
            Returns True if the path should be completely excluded.
            """
            relative_filepath = path.relative_to(self.root_dir).as_posix()
    
            # Check if any part of the path is in EXCLUDED_DIRS
            for part in path.parts:
                if part in Config.EXCLUDED_DIRS + self.exclude_dir_patterns:
                    if path.is_dir():
                        self.exclusion_counters['dirs'] += 1
                    else:
                        self.exclusion_counters['patterns'] += 1
                    return True
    
            # Check excluded regex patterns
            for pattern in Config.EXCLUDED_REGEX + self.exclude_regex:
                if re.match(pattern, relative_filepath):
                    self.exclusion_counters['regex'] += 1
                    return True
    
            # Check gitignore patterns
            for pattern in self.gitignore_patterns:
                if fnmatch.fnmatch(relative_filepath, pattern):
                    self.exclusion_counters['gitignore'] += 1
                    return True
    
            # Check excluded file patterns
            if path.is_file():
                for pattern in Config.EXCLUDED_PATTERNS + self.exclude_file_patterns:
                    if fnmatch.fnmatch(path.name, pattern):
                        self.exclusion_counters['patterns'] += 1
                        return True
    
            return False
    
    class MarkdownGenerator:
    
        # Markdown Generation Logic
        # =======================================================
        def __init__(self, root_dir: Path, output_file: Path, max_depth=None, extensions=None,
                     include_empty_dirs=False, exclude_dir_patterns=None, exclude_file_patterns=None,
                     include_all_files=False, show_all_files_in_filestructure=False, use_gitignore=False,
                     files_first=False):
            self.root_dir = root_dir
            self.output_file = output_file
            self.max_depth = max_depth
            self.extensions = self.resolve_extensions(extensions)
            self.include_empty_dirs = include_empty_dirs
            self.exclude_dir_patterns = exclude_dir_patterns
            self.exclude_file_patterns = exclude_file_patterns
            self.include_all_files = include_all_files
            self.show_all_files_in_filestructure = show_all_files_in_filestructure
            self.use_gitignore = use_gitignore
            self.files_first = files_first
            self.excluder = FileExcluder(
                root_dir=self.root_dir,
                exclude_dir_patterns=self.exclude_dir_patterns,
                exclude_file_patterns=self.exclude_file_patterns,
                use_gitignore=self.use_gitignore
            )
            self.console = Console()
    
        def resolve_extensions(self, extensions_list):
            logger.debug(f"Resolving extensions: {extensions_list}")
            resolved = []
            for item in extensions_list:
                if item in Config.EXTENSION_GROUPS:
                    resolved.extend(Config.EXTENSION_GROUPS[item])
                    logger.debug(f"Group '{item}' -> {Config.EXTENSION_GROUPS[item]}")
                else:
                    resolved.append(item)
                    logger.debug(f"Extension '{item}' added")
            logger.debug(f"Final extensions: {resolved}")
            return resolved
    
        def get_code_block_type(self, ext):
            return Config.CODE_BLOCK_TYPES.get(ext, ext)
    
        def generate_markdown_for_file(self, file_path: Path):
            relative_filepath = file_path.relative_to(self.root_dir)
            ext = file_path.suffix[1:]
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'utf-16', 'utf-16-le', 'utf-16-be']
            content = None
    
            try:
                with open(file_path, 'rb') as f:
                    raw = f.read(10000)
                detected = chardet.detect(raw)['encoding']
                if detected:
                    encodings.insert(0, detected)
            except Exception as e:
                logger.error(f"Encoding detection error for {file_path}: {e}")
    
            for enc in encodings:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.error(f"Error reading {file_path} with {enc}: {e}")
    
            if content is None:
                return f"#### `{relative_filepath}`\n\nError: Unable to read file.\n\n"
    
            # Current file header
            file_header = f"\n\n---\n\n#### `{relative_filepath}`\n\n"
    
            # Determine block type based on file extension
            block_type = self.get_code_block_type(ext)
            block_prefix = f"```{block_type}\n" if block_type else ""
            block_suffix = "\n```" if block_type else ""
    
            # Indent content by 4 spaces and combine elements
            lines = content.splitlines()
            indented_content = '\n'.join(f"    {line}" for line in lines)
            content_entry = f"{file_header}{block_prefix}{indented_content}{block_suffix}\n\n"
    
            return content_entry
    
        def build_tree(self, paths):
            tree = {}
            for p in paths:
                parts = p.relative_to(self.root_dir).parts
                node = tree
                for part in parts[:-1]:
                    node = node.setdefault(part, {})
                node.setdefault(parts[-1], {})
            return tree
    
        def is_content_excluded_path(self, path):
            """Check if a path is in the content-excluded files set"""
            return path in self.excluder.content_only_excluded_files
    
        def print_tree(self, node, prefix="", current_path=None):
            lines = []
            keys = list(node.keys())
    
            if current_path is None:
                current_path = self.root_dir
    
            # Separate files and directories
            files = [k for k in keys if not node[k]]  # Empty dict means it's a file
            dirs = [k for k in keys if node[k]]       # Non-empty dict means it's a directory
    
            # Sort files and directories separately
            files.sort()
            dirs.sort()
    
            # Combine files and directories based on files_first parameter
            sorted_keys = files + dirs if self.files_first else dirs + files
    
            for i, key in enumerate(sorted_keys):
                connector = "└──" if i == len(sorted_keys)-1 else "├──"
    
                # For files, check if content-excluded
                if key in files:
                    path = current_path / key
                    if self.excluder.is_content_excluded(path):
                        lines.append(f"{prefix}{connector} {key} {Config.CONTENT_EXCLUDED_MARKER}")
                    else:
                        lines.append(f"{prefix}{connector} {key}")
                else:
                    # For directories, no exclusion marker
                    lines.append(f"{prefix}{connector} {key}")
    
                sub_node = node[key]
                if sub_node:  # If it's a directory
                    extension = "    " if i == len(sorted_keys)-1 else "│   "
                    next_path = current_path / key
                    lines.extend(self.print_tree(sub_node, prefix=prefix+extension, current_path=next_path))
            return lines
    
        def generate_markdown(self):
            try:
                gitignore_patterns = self.excluder.gitignore_patterns
                if gitignore_patterns:
                    self.exclude_dir_patterns = (self.exclude_dir_patterns or []) + gitignore_patterns
                    self.exclude_file_patterns = (self.exclude_file_patterns or []) + gitignore_patterns
    
                logger.debug(f"Generating markdown for {self.root_dir} -> {self.output_file}")
                logger.debug(f"Extensions: {self.extensions}, Include all: {self.include_all_files}, Show all in structure: {self.show_all_files_in_filestructure}")
    
                structure_patterns = ["*"] if self.include_all_files or self.show_all_files_in_filestructure else [f"*.{ext}" for ext in self.extensions]
                content_patterns = ["*"] if self.include_all_files else [f"*.{ext}" for ext in self.extensions]
    
                markdown_content = f"# Dir `{self.root_dir.stem}`\n\n"
                # Always show the marker explanation if content-excluded files can appear in the structure
                # markdown_content += f"*Files marked `{Config.CONTENT_EXCLUDED_MARKER}` are shown in structure but not included in content.*\n\n"
    
                excluded = []
                processed = []
                counters = self.excluder.exclusion_counters
    
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    MofNCompleteColumn(),
                    TimeElapsedColumn(),
                    console=self.console,
                ) as progress:
                    paths = sorted(self.root_dir.rglob("*"))
                    total = len(paths)
                    logger.debug(f"Found {total} paths.")
                    task1 = progress.add_task("[cyan]Gathering file structure...", total=total)
    
                    included_paths = []
                    for path in paths:
                        if self.excluder.is_excluded(path):
                            excluded.append(path)
                        else:
                            if ((self.max_depth is None or len(path.relative_to(self.root_dir).parts) <= self.max_depth)
                                and (path.is_dir() or any(fnmatch.fnmatch(path.name, pat) for pat in structure_patterns))
                                and (self.include_empty_dirs or not path.is_dir() or
                                     any(
                                         not self.excluder.is_excluded(f) and f.is_file() and any(fnmatch.fnmatch(f.name, p) for p in structure_patterns)
                                         for f in path.rglob("*")
                                     ))
                               ):
                                included_paths.append(path)
                        progress.update(task1, advance=1)
    
                    tree = self.build_tree(included_paths)
                    file_structure_lines = self.print_tree(tree)
                    file_structure_str = "### File Structure\n\n```\n" + "\n".join(file_structure_lines) + "\n```\n"
                    markdown_content += file_structure_str
    
                    progress.update(task1, completed=total)
                    logger.debug(f"Excluded {len(excluded)} paths: {counters}")
    
                    # Files to process for content (excluding content-only excluded files)
                    files = [p for p in paths if p not in excluded and p.is_file()
                             and not self.excluder.is_content_excluded(p)
                             and (self.max_depth is None or len(p.relative_to(self.root_dir).parts) <= self.max_depth)
                             and any(fnmatch.fnmatch(p.name, pat) for pat in content_patterns)]
    
                    # Sort files based on files_first parameter
                    def sort_key(path):
                        parts = path.relative_to(self.root_dir).parts
                        if self.files_first:
                            # For files-first, we want files in the current directory to come before subdirectories
                            return (len(parts), 0 if len(parts) == 1 else 1) + parts
                        else:
                            # For directories-first, we want files in subdirectories to be grouped with their directories
                            return parts
    
                    files.sort(key=sort_key)
                    logger.debug(f"{len(files)} files to process.")
                    task2 = progress.add_task("[cyan]Processing files for content...", total=len(files))
    
                    for idx, file in enumerate(files, 1):
                        try:
                            content = self.generate_markdown_for_file(file)
                            markdown_content += f"{content}"
                            processed.append(file)
                        except Exception as e:
                            logger.error(f"Failed on {file}: {e}")
                        finally:
                            progress.update(task2, advance=1)
    
                    logger.debug(f"Processed {len(processed)} files.")
    
                # Removes consecutive blank lines (preserving at most one blank line between text lines).
                markdown_content_clean = re.sub(r"\n{2,}", "\n\n", markdown_content)
                self.output_file.write_text(markdown_content_clean, encoding="utf-8")
                logger.info(f"Markdown generated at {self.output_file}")
    
                # Return the cleaned content for potential clipboard use
                return markdown_content_clean
    
            except Exception as e:
                logger.error(f"Markdown generation failed: {e}")
                raise
    
    class MarkdownGeneratorApp:
    
        # Main Application Logic
        # =======================================================
        def __init__(self):
            LoggerSetup.initialize_logging()
            self.arg_handler = ArgumentHandler()
    
        @staticmethod
        def ensure_md_extension(filename):
            return f"{filename}.md" if not filename.endswith('.md') else filename
    
        @staticmethod
        def ensure_directory_exists(directory: Path):
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created output directory: {directory}")
    
        @staticmethod
        def clear_console():
            os.system("cls" if platform.system() == "Windows" else "clear")
    
        @staticmethod
        def copy_to_clipboard(content: str) -> None:
            """Copy content to clipboard."""
            try:
                pyperclip.copy(content)
                Console().print("[bold green]✓ Markdown copied to clipboard[/bold green]")
            except Exception as e:
                Console().print(f"[bold yellow]Warning: Could not copy to clipboard: {e}[/bold yellow]")
    
        @staticmethod
        def display_summary(console, args):
            table = Table(title="Configuration Summary", show_header=True, header_style="bold magenta", box=box.ASCII)
            table.add_column("Parameter", style="dim", width=30)
            table.add_column("Value", style="bold cyan")
    
            summary_data = [
                ("Input directory path", str(args.input_path)),
                ("Output markdown file path", str(args.output_path)),
                ("Output filename", str(args.output_filename)),
                ("Maximum directory depth", str(args.depth)),
                ("Include all files", "Yes" if args.include_all_files else "No"),
                ("Show all files in filestructure", "Yes" if args.show_all_files_in_filestructure else "No"),
                ("Include empty directories", "Yes" if args.include_empty_dirs else "No"),
                ("Files before directories", "Yes" if args.files_first else "No"),
                ("Excluded directory patterns", ', '.join(args.exclude_dir_patterns) if args.exclude_dir_patterns else "None"),
                ("Excluded file patterns", ', '.join(args.exclude_file_patterns) if args.exclude_file_patterns else "None"),
                ("Use .gitignore for exclusions", "Yes" if args.use_gitignore else "No"),
                ("Cleanup log files after success", "Yes" if args.cleanup_logs else "No"),
            ]
    
            if not args.include_all_files:
                summary_data.insert(6, ("File extensions", ', '.join(args.extensions)))
    
            for param, value in summary_data:
                table.add_row(param, value)
    
            console.print(table)
    
        def run(self):
            logger.debug("Main started.")
            args = self.arg_handler.get_arguments()
            logger.debug(f"Arguments: {args}")
    
            # Get user inputs using the new pattern
            args = self.arg_handler.get_user_inputs(args)
            logger.debug(f"Post-input arguments: {args}")
    
            if not args.input_path or not args.output_path:
                Console().print("[bold red]Error: Input and output directories required.[/bold red]")
                logger.error("Input and output directories required.")
                return
    
            input_path = Path(args.input_path)
            output_path = Path(args.output_path)
            output_filename = self.ensure_md_extension(args.output_filename) if args.output_filename else "py__MarkdownGenerator.md"
            full_output = output_path / output_filename
    
            logger.debug(f"Input: {input_path}, Output: {output_path}, Filename: {output_filename}")
    
            self.ensure_directory_exists(output_path)
            self.display_summary(Console(), args)
    
            try:
                generator = MarkdownGenerator(
                    root_dir=input_path,
                    output_file=full_output,
                    max_depth=args.depth,
                    extensions=args.extensions,
                    include_empty_dirs=args.include_empty_dirs,
                    exclude_dir_patterns=args.exclude_dir_patterns,
                    exclude_file_patterns=args.exclude_file_patterns,
                    include_all_files=args.include_all_files,
                    show_all_files_in_filestructure=args.show_all_files_in_filestructure,
                    use_gitignore=args.use_gitignore,
                    files_first=args.files_first,
                )
                markdown_content = generator.generate_markdown()
                Console().print(f"\nMarkdown generated at [bold cyan]{full_output}[/bold cyan]\n")
    
                # Copy to clipboard if requested
                if getattr(args, 'copy_to_clipboard', True):
                    self.copy_to_clipboard(markdown_content)
    
                # Cleanup log file if specified and successful execution
                if args.cleanup_logs:
                    self.cleanup_logs()
    
            except Exception as e:
                logger.error(f"Generation failed: {e}")
                Console().print(f"\n[bold red]Error:[/bold red] {e}\n")
                raise
    
        def cleanup_logs(self):
            """Clean up log files after successful execution"""
            logger.remove()
            log_file = Path("app.log.yml")
            if log_file.exists():
                try:
                    log_file.unlink()
                    Console().print(f"Log file [bold green]{log_file}[/bold green] has been cleaned up.\n")
                except Exception as e:
                    Console().print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")
            else:
                Console().print(f"No log file found to clean up at {log_file}\n")
    
    def wait_for_user_exit():
        """Wait for user to press any key before exiting"""
        console = Console()
        console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
        try:
            input()
        except KeyboardInterrupt:
            pass
    
    def main():
        try:
            app = MarkdownGeneratorApp()
            app.run()
        except Exception as e:
            console = Console()
            console.print(f"\n[bold red]Error:[/bold red] {e}")
            logger.exception(f"Main execution error: {e}")
        finally:
            console = Console()
            console.print("\n[bold green]Finished processing.[/bold green]")
            wait_for_user_exit()
    
    if __name__ == "__main__":
        main()
```

