@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: gallery-dl Upgrade Tool
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  gallery-dl Upgrade Tool
ECHO ========================================
ECHO.

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: Show current gallery-dl version
ECHO [INFO] Current gallery-dl version:
uv run python -c "import gallery_dl; print(f'gallery-dl {gallery_dl.__version__}')" 2>nul || ECHO Not installed or not accessible
ECHO.

:: Upgrade gallery-dl
ECHO [INFO] Upgrading gallery-dl to latest version...
uv sync --upgrade-package gallery-dl

IF ERRORLEVEL 1 (
    ECHO [WARNING] uv sync failed, trying alternative method...
    uv pip install --upgrade gallery-dl
    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to upgrade gallery-dl
        PAUSE>NUL & EXIT /B
    ) ELSE (
        ECHO [SUCCESS] gallery-dl upgraded successfully
    )
) ELSE (
    ECHO [SUCCESS] gallery-dl upgraded successfully
)

:: Show new version
ECHO.
ECHO [INFO] New gallery-dl version:
uv run python -c "import gallery_dl; print(f'gallery-dl {gallery_dl.__version__}')"

ECHO.
ECHO Press any key to exit...
PAUSE >NUL
EXIT /B
