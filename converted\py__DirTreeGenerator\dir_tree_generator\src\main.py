#!/usr/bin/env python3
"""
Directory Tree Generator

A tool for generating visual representations of directory structures.
This script maps the structure of a directory and its subdirectories,
creating a text-based tree visualization that can be saved to a file.

Features:
- Map directory structures with customizable filtering
- Display child count for each directory (number of immediate files and subdirectories)
- Exclude specific file extensions or patterns
- Include or exclude empty directories
- Optionally only include certain file extensions
- Save the visualization to a file
- Open the file with the default application
- Remove the file after viewing (optional)
"""

import argparse
import os
import sys
import time
from pathlib import Path
from typing import Dict, Set, Optional

# Third-party imports
import pyperclip
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.theme import Theme

# Constants
EXCLUDED_PATTERNS = {
    ".cache",
    "__pycache__",
    ".backups",
    "*.egg-info",
    ".egg-info",
    "venv",
    ".venv",
    ".git",
    "node_modules",
}

# Custom theme for consistent styling
custom_theme = Theme(
    {
        "info": "cyan",
        "warning": "yellow",
        "error": "red",
        "success": "green",
        "prompt": "bold cyan",
        "path": "blue underline",
        "highlight": "bold magenta",
        "option": "green",
    }
)

# Initialize console for rich output
console = Console(theme=custom_theme)


# ============================================================================
# Path Utilities
# ============================================================================

def make_long_path_safe(path: Path) -> Path:
    """
    Convert path to long-path-safe format on Windows using \\\\?\\ prefix.
    This bypasses the 260-character MAX_PATH limitation and allows paths up to ~32,767 characters.
    """
    if os.name == 'nt':  # Windows only
        path_str = str(path.resolve())
        # Check if path is approaching the 260-character limit (use 240 as buffer)
        if len(path_str) > 240:
            # Add \\?\ prefix if not already present
            if not path_str.startswith('\\\\?\\'):
                return Path(f"\\\\?\\{path_str}")
    return path


# ============================================================================
# Directory Structure Functions
# ============================================================================

def get_filtered_contents(dir_path: Path, exclude_exts: Set[str], include_exts: Set[str]) -> list:
    """
    Gathers and filters directory contents based on extension, excluding specific patterns.

    Parameters:
    - dir_path (Path): Directory path to scan.
    - exclude_exts (Set[str]): Set of file extensions to exclude.
    - include_exts (Optional[Set[str]]): Set of file extensions to include only (if provided).

    Returns:
    - filtered_contents (list): List of filtered directory contents.
    """
    filtered_contents = []
    try:
        # Use long path support for directory iteration
        safe_dir_path = make_long_path_safe(dir_path)
        # Sort files so that directories come first
        for item in sorted(safe_dir_path.iterdir(), key=lambda x: (make_long_path_safe(x).is_file(), x.name)):
            # Use safe path for all operations on the item
            safe_item = make_long_path_safe(item)

            # Skip if excluded by name pattern
            if item.name in EXCLUDED_PATTERNS:
                continue

            # If 'include_exts' is in use, skip files that don't match
            if include_exts and safe_item.is_file() and item.suffix not in include_exts:
                continue

            # Otherwise, skip if it's in the exclude list
            if any(item.name.endswith(ext) for ext in exclude_exts):
                continue

            filtered_contents.append(item)

    except PermissionError:
        print(f"Skipping protected directory: {dir_path}")

    return filtered_contents


def map_structure(
    root_path: Path,
    include_empty: bool,
    exclude_exts: Set[str],
    include_exts: Optional[Set[str]] = None
) -> Dict[str, tuple]:
    """
    Constructs a nested dictionary representing the directory/file structure.

    Parameters:
    - root_path (Path): Root directory path to map.
    - include_empty (bool): Include empty directories if True.
    - exclude_exts (Set[str]): Set of file extensions to exclude.
    - include_exts (Optional[Set[str]]): Set of file extensions to include only (if provided).

    Returns:
    - structure (Dict[str, tuple]): Nested dictionary representing the structure.
      For directories, the value is a tuple (child_count, sub_structure).
      For files, the value is None.
    """
    structure = {}
    filtered_contents = get_filtered_contents(root_path, exclude_exts, include_exts)

    for item in filtered_contents:
        if item.is_dir():
            sub_structure = map_structure(item, include_empty, exclude_exts, include_exts)
            if sub_structure or include_empty:
                # Count immediate children (files + directories) in sub_structure
                child_count = len(sub_structure)
                structure[item.name] = (child_count, sub_structure)
        else:
            # It's a file; simply store None for files
            structure[item.name] = None

    return structure


def render_structure(
    structure: Dict[str, tuple],
    prefix: str = "",
    include_empty: bool = False,
    show_child_count: bool = True
) -> str:
    """
    Creates a visual representation from the structure map.

    Parameters:
    - structure (Dict[str, tuple]): Nested dictionary representing the structure.
      For directories, the value is a tuple (child_count, sub_structure).
      For files, the value is None.
    - prefix (str): Prefix for each line in the visual representation.
    - include_empty (bool): Include empty directories if True.
    - show_child_count (bool): Show the number of immediate children for directories if True.

    Returns:
    - visualization (str): Visual representation of the structure.
    """
    lines = []
    items = list(structure.items())
    for index, (name, sub_struct) in enumerate(items):
        is_last_item = (index == len(items) - 1)
        branch = "└── " if is_last_item else "├── "

        if sub_struct is not None:
            # Directory
            child_count, sub_structure_dict = sub_struct

            if show_child_count:
                current_line = f"{prefix}{branch}{name} ({child_count})"
            else:
                current_line = f"{prefix}{branch}{name}"

            sub_lines = render_structure(
                sub_structure_dict,
                prefix + ("    " if is_last_item else "│   "),
                include_empty,
                show_child_count,
            )
            if sub_lines:
                lines.append(current_line)
                lines.append(sub_lines)
            elif include_empty:
                lines.append(current_line)
        else:
            # File
            current_line = f"{prefix}{branch}{name}"
            lines.append(current_line)

    return "\n".join(lines)


# ============================================================================
# File Operations
# ============================================================================

def save_to_file(content: str, path: Path) -> None:
    """Saves content to the specified file."""
    try:
        safe_path = make_long_path_safe(path)
        safe_path.write_text(content, encoding="utf-8")
    except Exception as e:
        console.print(f"[error]Error saving to file:[/error] {str(e)}")
        raise


def open_file(path: Path) -> None:
    """Opens a file with the default application."""
    try:
        safe_path = make_long_path_safe(path)
        os.startfile(safe_path)
        console.print(f"[success]✓[/success] Opened file")
    except Exception as e:
        console.print(f"[error]Error opening file:[/error] {str(e)}")


def remove_file(path: Path) -> None:
    """Attempts to remove a file."""
    try:
        time.sleep(0.1)
        safe_path = make_long_path_safe(path)
        safe_path.unlink()
        console.print(f"[success]✓[/success] Removed file")
    except OSError as e:
        console.print(f"[error]Could not delete file:[/error] {str(e)}")


def copy_to_clipboard(content: str) -> None:
    """Copy content to clipboard."""
    try:
        pyperclip.copy(content)
        console.print("[success]✓[/success] Directory tree copied to clipboard")
    except Exception as e:
        console.print(f"[warning]Warning: Could not copy to clipboard: {e}[/warning]")


# ============================================================================
# Command-line Interface
# ============================================================================

def ensure_file_extension(filename: str, default_ext: str = ".md") -> str:
    """
    Ensure the filename has an extension, adding the default extension if none exists.

    Parameters:
    - filename (str): The filename to check
    - default_ext (str): The default extension to add if none exists (including the dot)

    Returns:
    - str: The filename with an extension
    """
    if not Path(filename).suffix:
        return f"{filename}{default_ext}"
    return filename


def parse_arguments() -> argparse.Namespace:
    """
    Parse command-line arguments.
    """
    parser = argparse.ArgumentParser(
        description="Visual Directory/File Structure Mapping.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("-i", "--input", type=Path, help="Input directory or file path.")
    parser.add_argument(
        "-op",
        "--output_path",
        type=Path,
        default=Path.cwd(),
        help="Output directory path.",
    )
    parser.add_argument(
        "-of",
        "--output_filename",
        type=str,
        default=f"{Path.cwd().name}.dirtree.md",
        help="Output file name (default extension: .md).",
    )
    parser.add_argument(
        "-ie",
        "--include_empty",
        action="store_true",
        help="Include empty directories.",
    )
    parser.add_argument(
        "-e",
        "--exclude_extensions",
        nargs="*",
        default=[],
        help="Extensions to exclude.",
    )
    parser.add_argument(
        "-o",
        "--open_file",
        default=True,
        action="store_true",
        help="Open the output file upon completion.",
    )
    parser.add_argument(
        "-r",
        "--remove_file",
        default=True,
        action="store_true",
        help="Remove the output file after it's been opened.",
    )
    parser.add_argument(
        "-nc",
        "--no_child_count",
        default=True,
        action="store_true",
        dest="hide_child_count",
        help="Hide the number of immediate children for directories.",
    )
    parser.add_argument(
        "-c",
        "--copy_to_clipboard",
        default=True,
        action="store_true",
        help="Copy the directory tree to clipboard",
    )
    parser.add_argument(
        "--prompt",
        action="store_true",
        help="Interactive mode - prompt for all inputs",
    )

    args = parser.parse_args()

    # Ensure output filename has an extension (default to .md)
    if args.output_filename:
        args.output_filename = ensure_file_extension(args.output_filename)

    return args


def print_section(title):
    """Print a styled section header"""
    console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)


def get_user_inputs(args: argparse.Namespace) -> argparse.Namespace:
    """
    Get user inputs with interactive prompts following converted project patterns.
    """
    # Handle missing required arguments first
    if args.prompt or not args.input:
        default_input = str(args.input) if args.input else str(Path.cwd())
        input_path_str = Prompt.ask("Enter the input directory or file path", default=default_input)
        args.input = Path(input_path_str)

    # Validate input path exists
    if not args.input or not args.input.exists():
        console.print("[bold red]Error: Input path does not exist or was not provided.[/bold red]")
        sys.exit(1)

    # If --prompt is used, ask for configuration options with improved UX
    if args.prompt:
        # Default Settings prompt comes first - key UX improvement
        print_section("Default Settings")
        use_defaults = Confirm.ask("Use default settings?", default=True)

        if use_defaults:
            # Apply sensible defaults and only ask for essential paths
            if not args.output_path:
                args.output_path = Path.cwd()
            if not args.output_filename:
                args.output_filename = f"{args.input.stem}.dirtree.md"

            # Set reasonable defaults for other options
            args.include_empty = False
            args.hide_child_count = True
            args.exclude_extensions = []
            args.include_only_extensions = []

            # Smart defaults: if copying to clipboard, no need to open/remove file
            args.copy_to_clipboard = True
            args.open_file = False  # Don't open if copying to clipboard
            args.remove_file = False  # Keep file since we're not opening it

            console.print("[dim]Using default settings with clipboard copy (no file opening)...[/dim]")
        else:
            # --- Post-Processing ---
            print_section("Post-Processing")
            args.copy_to_clipboard = Confirm.ask("Copy directory tree to clipboard?", default=True)

            # Smart defaults based on clipboard choice
            if args.copy_to_clipboard:
                # If copying to clipboard, default to not opening file (user has content in clipboard)
                args.open_file = Confirm.ask("Open the output file upon completion?", default=False)
                # If not opening, no point in removing
                if not args.open_file:
                    args.remove_file = False
                else:
                    args.remove_file = Confirm.ask("Remove the output file after opening?", default=args.remove_file)
            else:
                # If not copying to clipboard, default to opening file (user needs to see content)
                args.open_file = Confirm.ask("Open the output file upon completion?", default=True)
                if args.open_file:
                    args.remove_file = Confirm.ask("Remove the output file after opening?", default=args.remove_file)
                else:
                    args.remove_file = False

            # --- Input/Output ---
            print_section("Input/Output")
            if not args.output_path:
                default_output = str(Path.cwd())
                output_path_str = Prompt.ask("Output directory", default=default_output)
                args.output_path = Path(output_path_str)

            default_filename = args.output_filename if args.output_filename else f"{args.input.stem}.dirtree.md"
            args.output_filename = Prompt.ask("Output filename", default=default_filename)
            args.output_filename = ensure_file_extension(args.output_filename)

            # --- Display Options ---
            print_section("Display Options")
            args.include_empty = Confirm.ask("Include empty directories?", default=args.include_empty)
            args.hide_child_count = Confirm.ask("Hide child count for directories?", default=getattr(args, "hide_child_count", False))

            # --- File Filtering ---
            print_section("File Filtering")
            include_only = Confirm.ask("Include only specific file extensions?", default=False)
            include_only_extensions = []
            if include_only:
                extensions_input = Prompt.ask(
                    "Extensions to include only (space-separated, e.g., 'py md')",
                    default=""
                )
                if extensions_input.strip():
                    raw_includes = extensions_input.split()
                    include_only_extensions = [
                        (ext if ext.startswith(".") else f".{ext}") for ext in raw_includes
                    ]
            args.include_only_extensions = include_only_extensions

            # Only ask for exclusions if not using include-only mode
            if not include_only_extensions:
                exclude_input = Prompt.ask(
                    "Extensions to exclude (space-separated, e.g., 'log tmp')",
                    default=" ".join(args.exclude_extensions)
                )
                args.exclude_extensions = exclude_input.split() if exclude_input.strip() else []
    else:
        # Non-interactive mode - ensure we have output path
        if not args.output_path:
            args.output_path = Path.cwd()

    return args





def wait_for_user_exit():
    """Wait for user to press any key before exiting"""
    console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
    try:
        input()
    except KeyboardInterrupt:
        pass


def main() -> None:
    """Main function to run the script."""
    try:
        # Display a simple header
        console.print("[info]───────── Directory Tree Generator ─────────[/info]")
        console.print("Generate visual representations of directory structures")
        console.print("[info]───────────────── v1.1.0 ─────────────────[/info]")

        args = parse_arguments()

        # Get user inputs using the converted project pattern
        args = get_user_inputs(args)

        # Determine the directory to map
        safe_input = make_long_path_safe(args.input)
        if safe_input.is_file():
            # If user gave a file, we treat its parent directory as the root
            root_dir = args.input.parent
        else:
            # Otherwise, the input is a directory
            root_dir = args.input

        # Convert exclude_extensions to a set
        exclude_exts_set = set(args.exclude_extensions)

        # Convert include_only_extensions to a set (if any)
        include_only_extensions = getattr(args, "include_only_extensions", [])
        include_exts_set = set(include_only_extensions) if include_only_extensions else None

        # Map the structure
        structure = map_structure(
            root_dir,
            args.include_empty,
            exclude_exts_set,
            include_exts_set
        )

        # Get the hide_child_count parameter
        hide_child_count = getattr(args, "hide_child_count", True)

        # Render the final tree with root directory name
        tree_content = render_structure(
            structure,
            include_empty=args.include_empty,
            show_child_count=not hide_child_count
        )

        # Add root directory name at the top
        root_name = root_dir.name if root_dir.name else "root"
        if tree_content:
            visualization = f"└── {root_name}\n    │\n" + "\n".join(f"    {line}" for line in tree_content.split("\n"))
        else:
            visualization = f"└── {root_name}\n    │\n    (empty directory)"

        # Save the visualization
        output_file = args.output_path / args.output_filename
        save_to_file(visualization, output_file)

        # Copy to clipboard (always enabled by default for safety)
        if getattr(args, 'copy_to_clipboard', True):
            copy_to_clipboard(visualization)

        # Open and/or remove the file if requested
        if args.open_file:
            open_file(output_file)

        if args.remove_file:
            remove_file(output_file)

        # Show completion message
        if args.copy_to_clipboard and not args.remove_file:
            console.print(f"[success]✓[/success] Directory tree copied to clipboard and saved to [path]{output_file}[/path]")
        elif args.copy_to_clipboard and args.remove_file:
            console.print("[success]✓[/success] Directory tree copied to clipboard (file was removed)")
        elif not args.remove_file:
            console.print(f"[success]✓[/success] Directory tree saved to [path]{output_file}[/path]")

    except Exception as e:
        # Handle errors gracefully
        console.print(f"[error]Error:[/error] {str(e)}")
    finally:
        console.print("\n[bold green]Finished processing.[/bold green]")
        wait_for_user_exit()


if __name__ == "__main__":
    main()
